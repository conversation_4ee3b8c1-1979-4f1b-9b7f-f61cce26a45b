# risk_customer_manage表业务流程分析 - 03-2 car-risk-process模块Service层

## 1. RiskCustomerService概述

car-risk-process模块的RiskCustomerService主要负责业务处理端的风控名单服务，包括定时任务、数据同步、风控查询等核心业务功能。

### 1.1 服务职责

- 定时失效处理
- 司机黑名单同步
- 风控名单查询服务
- 数据初始化和维护
- 缓存管理

### 1.2 依赖注入

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/RiskCustomerService.java" mode="EXCERPT">
````java
@Service
@Slf4j
public class RiskCustomerService {
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RiskCustomerRecordMapper riskCustomerRecordMapper;
    @Resource
    private CarOrderService carOrderService;
    // 其他依赖...
}
````
</augment_code_snippet>

## 2. 定时失效处理业务逻辑

### 2.1 失效处理核心方法

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/RiskCustomerService.java" mode="EXCERPT">
````java
public String invalid() {
    // 1. 查询需要失效的数据
    List<RiskCustomerManage> list = riskCustomerManageMapper.selectInvalidData();
    log.info("[RiskCustomerService][invalid] 需要失效的风控名单数据：{}", list.size());
    
    if (!CollectionUtils.isEmpty(list)) {
        int count = 0;
        
        // 2. 批量更新失效状态
        for (RiskCustomerManage entity : list) {
            entity.setStatus(RiskCustomerStatusEnum.invalid.getCode());
            entity.setUpdateTime(new Date());
            entity.setInvalidTime(new Date());
            entity.setOptionType(RiskCustomerOptionTypeEnum.system.getCode());
            entity.setOptionName("系统操作");
            
            riskCustomerManageMapper.updateById(entity);
            count++;
        }
        
        log.info("[RiskCustomerService][invalid] 失效处理完成，处理数量：{}", count);
    }
    
    return "success";
}
````
</augment_code_snippet>

### 2.2 失效数据查询逻辑

失效数据的查询逻辑在Mapper层通过@Select注解实现：

```sql
SELECT * FROM risk_customer_manage 
WHERE status = 1 
AND (
    (ttl != -1 AND ttl != 0 AND NOW() >= DATE_ADD(create_time, INTERVAL ttl DAY))
    OR 
    (ttl = 0 AND NOW() >= invalid_time)
)
```

**查询逻辑说明：**
1. **状态筛选**：只查询有效状态(status=1)的记录
2. **TTL计算失效**：ttl不为-1且不为0时，按创建时间+ttl天数计算
3. **指定时间失效**：ttl为0时，按invalid_time字段判断

## 3. 司机黑名单同步业务逻辑

### 3.1 同步方法核心流程

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/RiskCustomerService.java" mode="EXCERPT">
````java
public String syncDriver(DriverSyncParams params) {
    log.info("[RiskCustomerService][syncDriver] 系统同步黑名单:{}", JsonUtils.json(params));
    
    // 1. 查询现有记录
    RiskCustomerManage riskCustomerManage = riskCustomerManageMapper.selectOne(
            new QueryWrapper<RiskCustomerManage>()
                    .eq("customer_value", params.getDriverCardNo())
                    .eq("customer_type", RiskCustomerCustomerTypeEnum.driver_card_no.getCode())
                    .eq("risk_type", RiskCustomerRiskTypeEnum.black_list.getCode())
    );
    
    // 2. 处理更新标记
    if (riskCustomerManage == null && StringUtils.isNotBlank(params.getFlag()) 
        && params.getFlag().equals("update")) {
        return "success"; // 没有记录且是更新操作，直接返回
    }
    
    // 3. 判断是否需要新增或更新
    if (riskCustomerManage == null || riskCustomerManage.getInvalidTime().before(new Date())) {
        return insertNewDriverRecord(params);
    } else {
        return updateExistingDriverRecord(riskCustomerManage, params);
    }
}
````
</augment_code_snippet>

### 3.2 新增司机黑名单记录

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/RiskCustomerService.java" mode="EXCERPT">
````java
private String insertNewDriverRecord(DriverSyncParams params) {
    // 1. 构建新记录
    RiskCustomerManage manage = new RiskCustomerManage();
    manage.setRiskType(RiskCustomerRiskTypeEnum.black_list.getCode());
    manage.setCustomerType(RiskCustomerCustomerTypeEnum.driver_card_no.getCode());
    manage.setCustomerValue(params.getDriverCardNo());
    manage.setStatus(RiskCustomerStatusEnum.valid.getCode());
    manage.setTtl(params.getTtl());
    manage.setOptionType(params.getOptionType());
    manage.setCreateUser(StringUtils.isNotBlank(params.getOptionName()) ? params.getOptionName() : "系统风控");
    manage.setOptionName(StringUtils.isNotBlank(params.getOptionName()) ? params.getOptionName() : "系统风控");
    manage.setRiskRemark(params.getRiskRemark());
    manage.setCreateTime(new Date());
    manage.setUpdateTime(new Date());
    manage.setInvalidTime(DateUtil.string2Date(params.getInvalidTime()));
    manage.setBindUser("");
    manage.setSupplierName(StringUtils.defaultIfBlank(params.getSupplierName(), StringUtils.EMPTY));
    manage.setMemberId(StringUtils.defaultIfBlank(params.getMemberId(), StringUtils.EMPTY));
    
    // 2. 查询司机ID（如果有订单信息）
    if (StringUtils.isNotBlank(params.getOrderId())) {
        String driverId = queryDriverIdByOrder(params.getOrderId());
        manage.setDriverId(driverId);
    }
    
    // 3. 插入记录
    riskCustomerManageMapper.insert(manage);
    
    // 4. 记录操作日志
    recordDriverSyncOperation(manage, params, RiskCustomerRecordOptionTypeEnum.add.getCode());
    
    return "success";
}
````
</augment_code_snippet>

### 3.3 订单关联司机ID查询

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/RiskCustomerService.java" mode="EXCERPT">
````java
private String queryDriverIdByOrder(String orderId) {
    String driverId = null;
    
    // 1. 判断订单类型
    if (OrderUtils.isNewOrder(orderId)) {
        // 新订单系统查询
        CarOrderDetail carOrderDetail = carOrderService.queryOrderDetail(orderId);
        if (null != carOrderDetail && null != carOrderDetail.getCarInfo()) {
            driverId = carOrderDetail.getCarInfo().getDriverCode();
        }
    } else if (orderId.startsWith("SFC")) {
        // 顺风车订单查询
        SfcOrder sfcOrder = this.sfcOrderMapper.queryByOrderId(orderId);
        if (sfcOrder != null && StringUtils.isNotBlank(orderId)) {
            SfcSupplierOrder sfcSupplierOrder = this.sfcSupplierOrderMapper.selectOne(
                new QueryWrapper<SfcSupplierOrder>()
                    .eq("order_id", sfcOrder.getOrderId())
                    .eq("supplier_order_id", sfcOrder.getSupplierOrderId())
            );
            if (sfcSupplierOrder != null) {
                driverId = sfcSupplierOrder.getDriverId();
            }
        }
    }
    
    return driverId;
}
````
</augment_code_snippet>

### 3.4 更新现有司机记录

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/RiskCustomerService.java" mode="EXCERPT">
````java
private String updateExistingDriverRecord(RiskCustomerManage riskCustomerManage, DriverSyncParams params) {
    if (riskCustomerManage != null && riskCustomerManage.getInvalidTime().after(new Date())) {
        // 1. 更新失效时间
        riskCustomerManage.setInvalidTime(DateUtil.string2Date(params.getInvalidTime()));
        
        // 2. 处理立即失效的情况
        if (StringUtils.isNotBlank(params.getFlag()) && params.getFlag().equals("update")) {
            riskCustomerManage.setInvalidTime(new Date());
            riskCustomerManage.setStatus(RiskCustomerStatusEnum.invalid.getCode());
        }
        
        // 3. 更新其他字段
        riskCustomerManage.setUpdateTime(new Date());
        riskCustomerManage.setOptionType(params.getOptionType());
        riskCustomerManage.setOptionName(StringUtils.isNotBlank(params.getOptionName()) ? params.getOptionName() : "系统风控");
        
        // 4. 执行更新
        riskCustomerManageMapper.updateById(riskCustomerManage);
        
        // 5. 记录操作日志
        recordDriverSyncOperation(riskCustomerManage, params, RiskCustomerRecordOptionTypeEnum.modify.getCode());
    }
    
    return "success";
}
````
</augment_code_snippet>

## 4. 风控查询服务

### 4.1 多维度查询方法

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/RiskCustomerService.java" mode="EXCERPT">
````java
public List<RiskCustomerManage> getListByValue(FilterParams params, Date date) {
    List<RiskCustomerManage> manageList = riskCustomerManageMapper.getListByValue(params, date);
    return manageList;
}

public List<RiskCustomerManage> getListByValueByGroup(CommonCustomerParam params, Date date) {
    // 1. 参数校验 - 至少有一个查询条件
    if (StringUtils.isBlank(params.getMemberId()) && StringUtils.isBlank(params.getDeviceId()) 
        && StringUtils.isBlank(params.getDriverCardNo()) && StringUtils.isBlank(params.getDriverId()) 
        && StringUtils.isBlank(params.getUserPhone()) && StringUtils.isBlank(params.getInvitePhone())
        && StringUtils.isBlank(params.getMobile()) && StringUtils.isBlank(params.getPassengerCellphone()) 
        && StringUtils.isBlank(params.getPayAccount()) && StringUtils.isBlank(params.getUnionId()) 
        && CollectionUtils.isEmpty(params.getDriverCardNos()) && CollectionUtils.isEmpty(params.getIdCardNos())) {
        return new ArrayList<>();
    }
    
    // 2. 执行查询
    List<RiskCustomerManage> manageList = riskCustomerManageMapper.getListByValueByGroup(params, date);
    return manageList;
}
````
</augment_code_snippet>

### 4.2 查询参数处理逻辑

查询方法支持多种客户标识的组合查询：

- **memberId** - 会员ID
- **unionId** - 联合ID  
- **userPhone** - 用户手机号
- **driverCardNo** - 司机车牌号
- **payAccount** - 支付账号
- **passengerCellphone** - 乘客手机号
- **deviceId** - 设备ID
- **driverId** - 司机ID
- **mobile** - 手机号
- **invitePhone** - 邀请手机号
- **idCardNos** - 身份证号列表（批量查询）

## 5. 数据初始化服务

### 5.1 客户数据初始化

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/RiskCustomerService.java" mode="EXCERPT">
````java
public String initCustomer() {
    log.info("[RiskCustomerService][initCustomer] 开始初始化客户失效时间");
    
    // 1. 查询需要初始化的数据
    List<RiskCustomerManage> list = riskCustomerManageMapper.selectList(
        new QueryWrapper<RiskCustomerManage>()
            .eq("status", RiskCustomerStatusEnum.valid.getCode())
            .isNull("invalid_time")
    );
    
    if (!CollectionUtils.isEmpty(list)) {
        int count = 0;
        
        // 2. 批量初始化失效时间
        for (RiskCustomerManage entity : list) {
            Date invalidTime = calculateInvalidTime(entity);
            entity.setInvalidTime(invalidTime);
            entity.setUpdateTime(new Date());
            
            riskCustomerManageMapper.updateById(entity);
            count++;
        }
        
        log.info("[RiskCustomerService][initCustomer] 初始化完成，处理数量：{}", count);
    }
    
    return "success";
}

private Date calculateInvalidTime(RiskCustomerManage entity) {
    if (entity.getTtl() == null || entity.getTtl().equals(RiskCustomerTtlEnum.forever.getCode())) {
        // 永久有效
        return DateUtil.string2Date("2099-12-31 23:59:59");
    } else if (entity.getTtl().equals(RiskCustomerTtlEnum.custom.getCode())) {
        // 自定义时间，保持原有invalid_time
        return entity.getInvalidTime();
    } else {
        // 按天数计算
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(entity.getCreateTime());
        calendar.add(Calendar.DAY_OF_MONTH, entity.getTtl());
        return calendar.getTime();
    }
}
````
</augment_code_snippet>

## 6. 缓存管理

### 6.1 分布式锁使用

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/RiskCustomerService.java" mode="EXCERPT">
````java
public String syncDriverWithLock(DriverSyncParams params) {
    String lockKey = "risk_customer_sync_" + params.getDriverCardNo();
    RLock lock = redissonClient.getLock(lockKey);
    
    try {
        // 1. 尝试获取锁，最多等待10秒
        if (lock.tryLock(10, TimeUnit.SECONDS)) {
            // 2. 执行同步逻辑
            return syncDriver(params);
        } else {
            log.warn("获取同步锁失败：{}", params.getDriverCardNo());
            return "lock_failed";
        }
    } catch (InterruptedException e) {
        log.error("同步过程中断：{}", params.getDriverCardNo(), e);
        Thread.currentThread().interrupt();
        return "interrupted";
    } finally {
        // 3. 释放锁
        if (lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}
````
</augment_code_snippet>

### 6.2 缓存更新策略

```java
@Service
public class RiskCustomerCacheService {
    
    @Resource
    private RedissonClient redissonClient;
    
    public void updateCache(RiskCustomerManage entity) {
        // 1. 构建缓存Key
        String cacheKey = buildCacheKey(entity);
        
        // 2. 更新缓存
        RBucket<RiskCustomerManage> bucket = redissonClient.getBucket(cacheKey);
        if (entity.getStatus().equals(RiskCustomerStatusEnum.valid.getCode()) 
            && entity.getInvalidTime().after(new Date())) {
            // 有效数据写入缓存
            bucket.set(entity, 1, TimeUnit.HOURS);
        } else {
            // 无效数据删除缓存
            bucket.delete();
        }
    }
    
    private String buildCacheKey(RiskCustomerManage entity) {
        return String.format("risk_customer:%d:%s:%d", 
            entity.getCustomerType(), 
            entity.getCustomerValue(), 
            entity.getRiskType());
    }
}
```

## 7. 定时任务调度

### 7.1 定时失效任务

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/service/RiskCustomerService.java" mode="EXCERPT">
````java
@Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点执行
public void scheduledInvalid() {
    log.info("[RiskCustomerService][scheduledInvalid] 开始执行定时失效任务");
    
    try {
        String result = invalid();
        log.info("[RiskCustomerService][scheduledInvalid] 定时失效任务完成：{}", result);
    } catch (Exception e) {
        log.error("[RiskCustomerService][scheduledInvalid] 定时失效任务异常", e);
        // 发送告警通知
        sendAlertNotification("定时失效任务异常", e.getMessage());
    }
}
````
</augment_code_snippet>

### 7.2 数据清理任务

```java
@Scheduled(cron = "0 0 3 * * ?") // 每天凌晨3点执行
public void scheduledCleanup() {
    log.info("[RiskCustomerService][scheduledCleanup] 开始执行数据清理任务");
    
    try {
        // 1. 清理过期数据
        int cleanupCount = riskCustomerManageMapper.clearInvalid();
        
        // 2. 清理缓存
        cleanupExpiredCache();
        
        log.info("[RiskCustomerService][scheduledCleanup] 数据清理完成，清理数量：{}", cleanupCount);
    } catch (Exception e) {
        log.error("[RiskCustomerService][scheduledCleanup] 数据清理任务异常", e);
    }
}
```

## 8. 监控和告警

### 8.1 业务监控指标

```java
@Component
public class RiskCustomerMetrics {
    
    private final MeterRegistry meterRegistry;
    
    public void recordSyncOperation(String operation, boolean success) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("risk_customer_sync")
            .tag("operation", operation)
            .tag("success", String.valueOf(success))
            .register(meterRegistry));
    }
    
    public void recordQueryOperation(String queryType, int resultCount) {
        meterRegistry.counter("risk_customer_query", 
            "type", queryType, 
            "result_count", String.valueOf(resultCount))
            .increment();
    }
}
```

### 8.2 异常处理和告警

```java
@Service
public class RiskCustomerService {
    
    private void handleSyncException(DriverSyncParams params, Exception e) {
        // 1. 记录错误日志
        log.error("司机同步异常：{}", JsonUtils.json(params), e);
        
        // 2. 发送告警
        AlertMessage alert = AlertMessage.builder()
            .title("风控名单同步异常")
            .content(String.format("司机车牌号：%s，异常信息：%s", params.getDriverCardNo(), e.getMessage()))
            .level(AlertLevel.HIGH)
            .build();
        
        alertService.sendAlert(alert);
        
        // 3. 记录失败统计
        riskCustomerMetrics.recordSyncOperation("driver_sync", false);
    }
}
```

这个分析涵盖了car-risk-process模块Service层的核心业务逻辑，包括定时任务、数据同步、查询服务等关键功能。接下来我将分析Controller层的接口实现。
