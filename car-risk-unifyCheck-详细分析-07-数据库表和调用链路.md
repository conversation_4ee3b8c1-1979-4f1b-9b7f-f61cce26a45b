# car-risk项目 `/riskCheck/unifyCheck` 接口详细分析 - 第7部分：数据库表和调用链路

## 1. 涉及的数据库表总览

unifyCheck接口在执行过程中会涉及多个数据库表，按照功能分类如下：

### 1.1 风控名单相关表

| 表名 | 用途 | 读写类型 | 调用时机 |
|------|------|----------|----------|
| risk_customer_manage | 风控客户管理主表 | 读/写 | 黑名单检查、策略动作执行 |
| risk_customer_record | 风控客户操作记录表 | 写 | 策略动作执行时记录操作 |
| hc_customer | 汇川客户表（司机端） | 读/写 | MT业务线黑名单检查 |

### 1.2 策略规则相关表

| 表名 | 用途 | 读写类型 | 调用时机 |
|------|------|----------|----------|
| metric_strategy | 风控策略表 | 读 | 策略加载时读取 |
| metric_rule | 风控规则表 | 读 | 策略加载时读取 |
| metric_field | 风控指标表 | 读 | 策略加载时读取 |
| metric_strategy_rule_relation | 策略规则关联表 | 读 | 策略加载时读取 |
| metric_rule_field_relation | 规则指标关联表 | 读 | 策略加载时读取 |
| metric_scene_strategy_relation | 场景策略关联表 | 读 | 策略加载时读取 |
| metric_scene | 场景表 | 读 | 场景信息查询 |

### 1.3 命中记录相关表

| 表名 | 用途 | 读写类型 | 调用时机 |
|------|------|----------|----------|
| risk_hit | 风控命中记录表 | 写 | 命中黑名单或策略时记录 |
| risk_hit_link | 风控命中关联表 | 写 | 策略命中时记录详细信息 |

### 1.4 订单相关表

| 表名 | 用途 | 读写类型 | 调用时机 |
|------|------|----------|----------|
| order_info | 订单基础信息表 | 读 | 订单完成场景补充订单信息 |
| order_address | 订单地址信息表 | 读 | 补充订单地址信息 |
| sfc_order | 顺风车订单表 | 读 | 顺风车订单信息查询 |
| sfc_supplier_order | 顺风车供应商订单表 | 读 | 顺风车供应商信息查询 |

### 1.5 指标数据相关表

| 表名 | 用途 | 读写类型 | 调用时机 |
|------|------|----------|----------|
| car_risk_order_detail | 风控订单详情表 | 读 | 指标计算时读取历史数据 |
| risk_order_manage | 风控订单管理表 | 读 | 指标计算时读取历史数据 |

## 2. 数据读写入口详细分析

### 2.1 risk_customer_manage表读写入口

#### 2.1.1 读取入口

**入口1：黑名单检查**
- **调用链路**: `BlackListHandler.blackListCheck()` → `CommonBlackListHandler.getAllCustomerList()` → `RiskCustomerService.getListByValueByGroup()` → `RiskCustomerManageMapper.getListByValueByGroup()`
- **SQL查询**:
```sql
SELECT * FROM risk_customer_manage 
WHERE status = 1 
AND invalid_time > #{invalidTime}
AND (
    (customer_type = 2 AND customer_value = #{params.userPhone}) OR
    (customer_type = 2 AND customer_value = #{params.passengerCellphone}) OR
    (customer_type = 1 AND customer_value = #{params.memberId}) OR
    (customer_type = 6 AND customer_value = #{params.driverCardNo}) OR
    (customer_type = 3 AND customer_value = #{params.unionId}) OR
    (customer_type = 4 AND customer_value = #{params.deviceId}) OR
    (customer_type = 5 AND customer_value = #{params.payAccount}) OR
    (customer_type = 10 AND customer_value IN (#{params.idCardNos}))
)
ORDER BY risk_type ASC, create_time DESC
```

**入口2：特殊场景检查**
- **调用链路**: `SpecialSceneHandler.check()` → `RiskCustomerService.getListByValue()` → `RiskCustomerManageMapper.getListByValue()`

#### 2.1.2 写入入口

**入口1：策略动作执行**
- **调用链路**: `RiskStrategyHandler.doStrategyAction()` → `DisposeCenterService.actionCustomer()` → `RiskCustomerService.addRiskCustomer()` → `RiskCustomerManageMapper.insert()`

**入口2：外部同步接口**
- **调用链路**: `RiskCustomerController.syncDriver()` → `RiskCustomerService.syncDriver()` → `RiskCustomerManageMapper.insert()`

### 2.2 策略相关表读取入口

**调用链路**: `RiskStrategyHandler.findStrategy()` → `RiskStrategyHelper.findStrategy()` → 内存缓存查询

**缓存加载链路**: `RiskStrategyHelper.initRiskStrategy()` → 多表关联查询
```java
// 1. 查询场景策略关系
List<RiskSceneStrategyDTO> sceneStrategyList = sceneStrategyRelationMapper.findSceneStrategy();

// 2. 查询策略规则关系  
List<RiskStrategyRuleDTO> strategyRuleList = sceneStrategyRelationMapper.findStrategyRule();

// 3. 查询规则指标关系
List<RiskRuleFieldDTO> ruleFieldList = sceneStrategyRelationMapper.findRuleField();

// 4. 查询指标详情
List<MetricField> fieldList = fieldMapper.selectList(new QueryWrapper<>());
```

### 2.3 risk_hit表写入入口

**调用链路**: `RiskStrategyHandler.strategyPostProcessing()` → `RiskHitService.initHitRisk()` → `RiskHitMapper.insert()`

## 3. 核心调用链路流程图

### 3.1 unifyCheck完整调用链路

```mermaid
graph TD
    A[HTTP POST /riskCheck/unifyCheck] --> B[RiskCheckController.unifyCheck]
    B --> C[RiskStrategyHandler.unifyCheck]
    
    C --> D[参数校验 paramCheck]
    D --> E[黑名单检查 BlackListHandler.blackListCheck]
    E --> F{命中黑名单?}
    F -->|是| G[返回拦截结果]
    F -->|否| H[分销单判断]
    H --> I{是分销单?}
    I -->|是| J[返回通过结果]
    I -->|否| K[特殊场景检查 SpecialSceneHandlerFactory.handlerCheck]
    K --> L{命中特殊规则?}
    L -->|是| M[返回特殊场景结果]
    L -->|否| N[策略检查前处理 beforeStrategyCheck]
    N --> O[策略检查 strategyCheck]
    O --> P[策略检查后处理 afterStrategyCheck]
    P --> Q[返回最终结果]
    
    subgraph "黑名单检查详细流程"
        E1[CommonBlackListHandler.blackListCheck]
        E2[getAllCustomerList]
        E3[RiskCustomerService.getListByValueByGroup]
        E4[RiskCustomerManageMapper.getListByValueByGroup]
        E5[数据库查询 risk_customer_manage]
    end
    
    subgraph "策略检查详细流程"
        O1[buildContext 构建上下文]
        O2[findStrategy 查找策略]
        O3[strategyHandle 执行策略]
        O4[strategyPostProcessing 后置处理]
    end
    
    E --> E1
    E1 --> E2
    E2 --> E3
    E3 --> E4
    E4 --> E5
    
    O --> O1
    O1 --> O2
    O2 --> O3
    O3 --> O4
```

### 3.2 策略检查内部调用链路

```mermaid
graph TD
    A[strategyCheck开始] --> B[fillParam 填充参数]
    B --> C[buildContext 构建上下文]
    C --> D[encryptParam 参数加密]
    D --> E[findStrategy 查找策略]
    E --> F[fillOrderParam 填充订单参数]
    F --> G[strategyHandle 执行策略]
    G --> H[strategyPostProcessing 后置处理]
    
    subgraph "findStrategy详细流程"
        E1[RiskStrategyHelper.findStrategy]
        E2[从内存缓存中过滤策略]
        E3[按业务线、渠道、场景过滤]
        E4[按供应商过滤]
    end
    
    subgraph "strategyHandle详细流程"
        G1[遍历策略列表]
        G2[checkStrategy 检查单个策略]
        G3[checkRule 检查规则]
        G4[执行指标脚本]
        G5[执行规则脚本]
        G6[执行策略脚本]
    end
    
    subgraph "strategyPostProcessing详细流程"
        H1[分析策略执行结果]
        H2[选择最高优先级策略]
        H3[记录命中信息]
        H4[执行策略动作]
        H5[RiskHitService.initHitRisk]
        H6[DisposeCenterService.actionCustomer]
    end
    
    E --> E1
    G --> G1
    H --> H1
```

### 3.3 数据库操作调用链路

```mermaid
graph TD
    A[unifyCheck接口调用] --> B{需要查询数据库?}
    
    B -->|黑名单检查| C[查询 risk_customer_manage]
    B -->|策略加载| D[查询策略相关表]
    B -->|订单信息| E[查询订单相关表]
    B -->|指标计算| F[查询指标数据表]
    
    C --> C1[RiskCustomerManageMapper.getListByValueByGroup]
    C1 --> C2[根据多个条件查询有效名单]
    
    D --> D1[定时任务每分钟执行]
    D1 --> D2[MetricSceneStrategyRelationMapper.findSceneStrategy]
    D2 --> D3[MetricSceneStrategyRelationMapper.findStrategyRule]
    D3 --> D4[MetricSceneStrategyRelationMapper.findRuleField]
    D4 --> D5[MetricFieldMapper.selectList]
    D5 --> D6[组装策略详情并缓存到内存]
    
    E --> E1[CarOrderService.queryOrderDetail]
    E1 --> E2[查询新订单系统]
    E1 --> E3[查询老订单系统]
    
    F --> F1[指标脚本中调用]
    F1 --> F2[查询历史订单数据]
    F1 --> F3[查询统计数据]
    
    subgraph "写入操作"
        G[策略命中后写入]
        G --> G1[RiskHitMapper.insert 记录命中信息]
        G --> G2[RiskCustomerManageMapper.insert 添加风控名单]
        G --> G3[RiskCustomerRecordMapper.insert 记录操作日志]
    end
    
    A --> G
```

## 4. 数据库表详细结构和索引

### 4.1 risk_customer_manage表结构

```sql
CREATE TABLE `risk_customer_manage` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `customer_type` int(11) NOT NULL COMMENT '客户类型',
  `customer_value` varchar(255) NOT NULL COMMENT '客户值',
  `risk_type` int(11) NOT NULL COMMENT '风险类型',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
  `ttl` int(11) NOT NULL DEFAULT '-1' COMMENT '有效期限',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `invalid_time` datetime DEFAULT NULL COMMENT '失效时间',
  `option_type` int(11) DEFAULT '1' COMMENT '操作类型',
  `create_user` varchar(255) DEFAULT NULL COMMENT '创建人',
  `option_name` varchar(255) DEFAULT NULL COMMENT '操作人',
  `risk_remark` text COMMENT '风险备注',
  `bind_user` varchar(255) DEFAULT NULL COMMENT '绑定用户',
  `bind_order` varchar(255) DEFAULT NULL COMMENT '绑定订单',
  `supplier_name` varchar(255) DEFAULT NULL COMMENT '供应商名称',
  PRIMARY KEY (`id`),
  KEY `idx_customer_value` (`customer_value`),
  KEY `idx_customer_type_risk_type` (`customer_type`, `risk_type`),
  KEY `idx_status_invalid_time` (`status`, `invalid_time`),
  KEY `idx_bind_user` (`bind_user`),
  KEY `idx_create_time` (`create_time`)
) COMMENT='风控客户管理表';
```

### 4.2 risk_hit表结构

```sql
CREATE TABLE `risk_hit` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `order_id` varchar(100) DEFAULT NULL COMMENT '订单ID',
  `main_scene` int(11) DEFAULT NULL COMMENT '主场景',
  `child_scene` int(11) DEFAULT NULL COMMENT '子场景',
  `member_id` varchar(100) DEFAULT NULL COMMENT '用户ID',
  `req_param` text COMMENT '请求参数',
  `risk_level` int(11) DEFAULT NULL COMMENT '风险等级',
  `hit_type` int(11) DEFAULT NULL COMMENT '命中类型',
  `hit_rule` varchar(500) DEFAULT NULL COMMENT '命中规则',
  `customer_value` varchar(255) DEFAULT NULL COMMENT '客户值',
  `customer_type` int(11) DEFAULT NULL COMMENT '客户类型',
  `product_line` varchar(50) DEFAULT NULL COMMENT '业务线',
  `request_id` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `res_result` text COMMENT '响应结果',
  `driver_card_no` varchar(100) DEFAULT NULL COMMENT '司机车牌号',
  `union_id` varchar(100) DEFAULT NULL COMMENT '微信unionId',
  `hit_strategy` varchar(500) DEFAULT NULL COMMENT '命中策略',
  `hit_field` varchar(100) DEFAULT NULL COMMENT '命中字段',
  `control_target` varchar(50) DEFAULT NULL COMMENT '管控对象',
  `dispose_action` varchar(50) DEFAULT NULL COMMENT '处置动作',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_driver_card_no` (`driver_card_no`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_product_line_scene` (`product_line`, `main_scene`, `child_scene`)
) COMMENT='风控命中记录表';
```

## 5. 性能优化和缓存机制

### 5.1 策略缓存机制

```java
// 策略数据使用CopyOnWriteArrayList缓存在内存中
private CopyOnWriteArrayList<RiskStrategyDetail> riskStrategy = new CopyOnWriteArrayList<>();

// 定时刷新机制，每分钟刷新一次
@Override
public void afterPropertiesSet() throws Exception {
    ScheduledExecutorService executorService = new ScheduledThreadPoolExecutor(1);
    executorService.scheduleWithFixedDelay(() -> initRiskStrategy(), 0, 1, TimeUnit.MINUTES);
}
```

### 5.2 ThreadLocal缓存

```java
// 请求级别的缓存，避免重复查询
private static final ThreadLocal<HashMap<String, List<CarRiskOrderDetail>>> localCache = 
    ThreadLocal.withInitial(() -> new HashMap<>());
private static final ThreadLocal<HashMap<String, Object>> localObjectCache = 
    ThreadLocal.withInitial(() -> new HashMap<>());
```

### 5.3 数据库查询优化

1. **索引优化**: 为常用查询字段建立复合索引
2. **分页查询**: 大数据量查询使用分页
3. **连接池**: 使用数据库连接池提高连接复用
4. **读写分离**: 读操作使用从库，写操作使用主库

## 6. 监控和日志

### 6.1 关键节点日志

```java
// 策略查找日志
LoggerUtils.info(log, "获取到对应策略:{} ", strategyNos);

// 规则执行日志  
LoggerUtils.info(log, "规则:{} 执行结果为:{}", rule.getRuleNo(), simpleRuleResult.getRuleMatched());

// 策略执行结果日志
LoggerUtils.info(log, "策略执行结果:{}", JSON.toJSONString(strategyResultMap));
```

### 5.2 性能监控

```java
@Component
public class RiskMetricsAOP {
    
    @Around("@annotation(RiskMetricsCache)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        try {
            Object result = joinPoint.proceed();
            long endTime = System.currentTimeMillis();
            // 记录执行时间
            recordMetrics(joinPoint.getSignature().getName(), endTime - startTime);
            return result;
        } catch (Exception e) {
            // 记录异常
            recordException(joinPoint.getSignature().getName(), e);
            throw e;
        }
    }
}
```

通过以上详细分析，我们可以清楚地看到unifyCheck接口涉及的所有数据库表、调用链路和性能优化措施，为系统的维护和优化提供了全面的参考。
