# car-risk项目 `/riskCheck/unifyCheck` 接口详细分析 - 第2部分：接口入口和参数校验

## 1. 接口入口分析

### 1.1 Controller层入口

**文件位置**: `car-risk-process/src/main/java/com/ly/car/risk/process/controller/RiskCheckController.java`

```java
@PostMapping("unifyCheck")
public UiResultWrapper unifyCheck(@RequestBody UnifyCheckRequest request) {
    LoggerUtils.initLogMap("unifyCheck", request.getProductLine(), request.getTraceId(), 
                          StringUtils.defaultIfBlank(request.getMemberId(), request.getCarNum()));
    try {
        LoggerUtils.info(log,"unifyCheck req:{}",JSON.toJSONString(request));
        RiskSceneResult result = riskStrategyHandler.unifyCheck(request);
        LoggerUtils.info(log,"unifyCheck resp:{}",JSON.toJSONString(result));
        return UiResultWrapper.ok(result);
    } catch (BizException e){
        LoggerUtils.error(log,"unifyCheck业务异常",e);
        return UiResultWrapper.fail(e.getCode(),e.getMessage());
    }catch (Exception e) {
        LoggerUtils.error(log,"unifyCheck系统异常",e);
        return UiResultWrapper.fail(-1,"系统异常");
    }finally {
        LoggerUtils.removeAll();
    }
}
```

### 1.2 接口调用链路

```
HTTP POST /riskCheck/unifyCheck
    ↓
RiskCheckController.unifyCheck()
    ↓
RiskStrategyHandler.unifyCheck()
    ↓
返回 RiskSceneResult
```

### 1.3 日志记录机制

接口使用了统一的日志记录机制：

1. **请求日志初始化**: 
   - 方法名: `unifyCheck`
   - 业务线: `request.getProductLine()`
   - 追踪ID: `request.getTraceId()`
   - 用户标识: `request.getMemberId()` 或 `request.getCarNum()`

2. **请求参数日志**: 完整记录请求参数JSON
3. **响应结果日志**: 完整记录响应结果JSON
4. **异常日志**: 区分业务异常和系统异常
5. **日志清理**: 在finally块中清理日志上下文

## 2. 请求参数详细分析

### 2.1 UnifyCheckRequest参数结构

**文件位置**: `car-risk-process/src/main/java/com/ly/car/risk/process/controller/request/UnifyCheckRequest.java`

```java
@Data
public class UnifyCheckRequest extends BaseRequest {
    
    // ========== 基础参数 ==========
    private String scene;              // 场景编码，如"1-1"表示司机注册
    private String productLine;        // 业务线：YNC(网约车)、SFC(顺风车)、MT(萌艇)等
    private String channel;            // 渠道：APP、H5、微信小程序等
    
    // ========== 用户相关参数 ==========
    private String memberId;           // 用户ID
    private String unionId;            // 微信unionId
    private String userPhone;          // 用户手机号
    private String passengerPhone;     // 乘客手机号
    private String payAccount;         // 支付账号
    private List<String> cardNos;      // 证件号列表
    private String deviceId;           // 设备ID
    private String ip;                 // IP地址
    
    // ========== 司机相关参数 ==========
    private String carNum;             // 车牌号
    
    // ========== 订单相关参数 ==========
    private String orderId;            // 订单ID
    
    // ========== 供应商相关参数 ==========
    private String supplierCode;       // 供应商编码
    private String supplierName;       // 供应商名称
    
    // ========== 业务标识参数 ==========
    private boolean distributionFlag;  // 分销单标识
    private boolean rightsOrderFlag;   // 权益单标识
    
    // ========== 扩展参数 ==========
    private Map<String, Object> ext = new HashMap<>();  // 扩展参数
    
    // ========== 内部调用参数 ==========
    private List<String> checkModule = new ArrayList<>();  // 需要校验的模块
    
    // ========== 命中信息统计参数 ==========
    private Integer hitType;           // 命中类型：0-策略，1-名单
    private String hitRule;            // 命中规则
    private String customerValue;      // 命中的客户值
    private Integer customerType;      // 命中的客户类型
    private String strategyNos;        // 命中的策略编号
    private String hitField;           // 命中字段
    private String controlTarget;      // 管控对象
    private String disposeAction;      // 处置动作
}
```

### 2.2 参数分类说明

#### 2.2.1 必填参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| scene | String | 场景编码 | "5-1"（司机接单） |
| productLine | String | 业务线 | "YNC"（网约车） |
| channel | String | 渠道（MT业务线除外） | "APP" |

#### 2.2.2 场景相关必填参数

| 场景 | 必填参数 | 说明 |
|------|----------|------|
| DRIVER_REGISTER (1-1) | deviceId | 司机注册需要设备ID |
| USER_CREATE_ORDER (2-1) | memberId | 用户下单需要用户ID |
| USER_DISPATCHING_ORDER (2-2) | memberId | 用户派单需要用户ID |
| ACT_COUPON_LQ (9-1) | memberId 或 unionId | 活动领券需要用户标识 |
| CANCEL_REMINDER (10-1) | orderId | 取消提醒需要订单ID |

#### 2.2.3 可选参数

其他参数根据具体业务场景选择性传入，用于风控策略的判断和记录。

## 3. 参数校验详细分析

### 3.1 参数校验入口

**文件位置**: `car-risk-process/src/main/java/com/ly/car/risk/process/strategy/RiskStrategyHandler.java`

```java
private void paramCheck(UnifyCheckRequest request) throws BizException {
    // 1. 初始化扩展参数
    if (null == request.getExt()) {
        request.setExt(new HashMap<>());
    }
    
    // 2. 基础参数校验
    CheckUtil.notBlankCheck(request.getScene(), "场景值不可为空");
    CheckUtil.notBlankCheck(request.getProductLine(), "业务线不可为空");
    
    // 3. 渠道参数校验（MT业务线除外）
    if (!Objects.equals(request.getProductLine(), ProductLineEnum.MT.getCode())) {
        CheckUtil.notBlankCheck(request.getChannel(), "渠道不可为空");
    }
    
    // 4. 场景枚举校验
    StrategySceneEnum sceneEnum = StrategySceneEnum.of(request.getScene());
    CheckUtil.notNullCheck(sceneEnum, "场景值异常");
    
    // 5. 场景特定参数校验
    switch (sceneEnum) {
        case DRIVER_REGISTER:
            CheckUtil.notBlankCheck(request.getDeviceId(), "deviceId不可为空");
            break;
        case USER_CREATE_ORDER:
        case USER_DISPATCHING_ORDER:
            CheckUtil.notBlankCheck(request.getMemberId(), "memberId不可为空");
            break;
        case ACT_COUPON_LQ:
            CheckUtil.notAllNullCheck("memberId和unionId不可同时为空", 
                                    request.getMemberId(), request.getUnionId());
            break;
        case CANCEL_REMINDER:
            CheckUtil.notBlankCheck(request.getOrderId(),"orderId不可为空");
            break;
        case DRIVER_ACCEPT_ORDER:
        default:
            break;
    }
}
```

### 3.2 校验工具类分析

**CheckUtil工具类**提供了多种校验方法：

```java
// 非空字符串校验
CheckUtil.notBlankCheck(String value, String message)

// 非null对象校验  
CheckUtil.notNullCheck(Object value, String message)

// 多个参数不能同时为空校验
CheckUtil.notAllNullCheck(String message, Object... values)
```

### 3.3 场景枚举定义

**StrategySceneEnum**定义了所有支持的业务场景：

```java
public enum StrategySceneEnum {
    DRIVER_REGISTER("1-1", "司机注册"),
    USER_CREATE_ORDER("2-1", "用户下单"),
    USER_DISPATCHING_ORDER("2-2", "用户派单"),
    DRIVER_ACCEPT_ORDER("5-1", "司机接单"),
    DISPATCHER_ACCEPT_ORDER("5-2", "调度员接单"),
    DRIVER_AUTHENTICATION("6-1", "司机认证"),
    BANK_CARD_CHANGE("7-1", "银行卡变更"),
    FINISH_ORDER("8-1", "订单完成"),
    ACT_COUPON_LQ("9-1", "活动领券"),
    CANCEL_REMINDER("10-1", "取消提醒"),
    RIGHTS_ORDER("11-1", "权益订单");
    
    private final String scene;
    private final String desc;
    
    // 根据场景编码获取枚举
    public static StrategySceneEnum of(String scene) {
        for (StrategySceneEnum sceneEnum : values()) {
            if (Objects.equals(sceneEnum.scene, scene)) {
                return sceneEnum;
            }
        }
        return null;
    }
}
```

### 3.4 业务线枚举定义

**ProductLineEnum**定义了支持的业务线：

```java
public enum ProductLineEnum {
    YNC("YNC", "网约车"),
    SFC("SFC", "顺风车"),
    MT("MT", "萌艇"),
    BUS("BUS", "汽车票"),
    LINE("LINE", "班线");
    
    private final String code;
    private final String desc;
}
```

## 4. 参数校验流程图

```mermaid
graph TD
    A[接收UnifyCheckRequest] --> B[初始化ext参数]
    B --> C[校验scene非空]
    C --> D[校验productLine非空]
    D --> E{是否为MT业务线?}
    E -->|否| F[校验channel非空]
    E -->|是| G[跳过channel校验]
    F --> G
    G --> H[校验scene枚举有效性]
    H --> I{根据场景类型校验}
    I -->|DRIVER_REGISTER| J[校验deviceId非空]
    I -->|USER_CREATE_ORDER/USER_DISPATCHING_ORDER| K[校验memberId非空]
    I -->|ACT_COUPON_LQ| L[校验memberId或unionId至少一个非空]
    I -->|CANCEL_REMINDER| M[校验orderId非空]
    I -->|其他场景| N[无额外校验]
    J --> O[校验通过]
    K --> O
    L --> O
    M --> O
    N --> O
    O --> P[进入下一步处理]
    
    C -->|校验失败| Q[抛出BizException]
    D -->|校验失败| Q
    F -->|校验失败| Q
    H -->|校验失败| Q
    J -->|校验失败| Q
    K -->|校验失败| Q
    L -->|校验失败| Q
    M -->|校验失败| Q
```

## 5. 异常处理机制

### 5.1 异常类型

1. **BizException**: 业务异常，参数校验失败时抛出
2. **Exception**: 系统异常，代码执行过程中的未预期异常

### 5.2 异常处理流程

```java
try {
    // 执行风控检查逻辑
    RiskSceneResult result = riskStrategyHandler.unifyCheck(request);
    return UiResultWrapper.ok(result);
} catch (BizException e){
    // 业务异常：返回具体错误码和错误信息
    LoggerUtils.error(log,"unifyCheck业务异常",e);
    return UiResultWrapper.fail(e.getCode(),e.getMessage());
}catch (Exception e) {
    // 系统异常：返回通用错误信息
    LoggerUtils.error(log,"unifyCheck系统异常",e);
    return UiResultWrapper.fail(-1,"系统异常");
}finally {
    // 清理日志上下文
    LoggerUtils.removeAll();
}
```

### 5.3 响应结果封装

**UiResultWrapper**统一封装响应结果：

```java
// 成功响应
UiResultWrapper.ok(result)

// 失败响应  
UiResultWrapper.fail(errorCode, errorMessage)
```

## 6. 调用示例

### 6.1 司机接单场景调用示例

```json
{
    "scene": "5-1",
    "productLine": "YNC", 
    "channel": "APP",
    "carNum": "京A12345",
    "passengerPhone": "13800138000",
    "orderId": "YNC202412260001",
    "supplierCode": "SUPPLIER001",
    "traceId": "trace-123456789"
}
```

### 6.2 用户下单场景调用示例

```json
{
    "scene": "2-1",
    "productLine": "SFC",
    "channel": "H5", 
    "memberId": "123456789",
    "userPhone": "13900139000",
    "ip": "*************",
    "traceId": "trace-987654321"
}
```

### 6.3 司机注册场景调用示例

```json
{
    "scene": "1-1",
    "productLine": "YNC",
    "channel": "APP",
    "deviceId": "device-abc123",
    "carNum": "沪B67890",
    "traceId": "trace-456789123"
}
```

通过以上详细分析，我们可以看到unifyCheck接口在参数校验方面做了充分的考虑，既保证了参数的完整性和有效性，又针对不同业务场景做了差异化的校验逻辑，为后续的风控处理奠定了坚实的基础。
