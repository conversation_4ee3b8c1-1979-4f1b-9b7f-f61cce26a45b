#!/bin/bash

# risk_customer_manage表业务流程分析文档打包脚本
# 创建时间: $(date)

echo "开始打包 risk_customer_manage 表业务流程分析文档..."

# 创建临时目录
TEMP_DIR="risk_customer_manage_analysis_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$TEMP_DIR"

# 复制所有分析文档
echo "复制分析文档..."
cp "risk_customer_manage表业务流程分析-01-项目总览和架构.md" "$TEMP_DIR/"
cp "risk_customer_manage表业务流程分析-02-数据访问层详细分析.md" "$TEMP_DIR/"
cp "risk_customer_manage表业务流程分析-03-1-manage模块Service层.md" "$TEMP_DIR/"
cp "risk_customer_manage表业务流程分析-03-2-process模块Service层.md" "$TEMP_DIR/"
cp "risk_customer_manage表业务流程分析-04-Controller层接口详细分析.md" "$TEMP_DIR/"
cp "risk_customer_manage表业务流程分析-05-规则引擎集成分析.md" "$TEMP_DIR/"
cp "risk_customer_manage表业务流程分析-06-调用链路详细分析.md" "$TEMP_DIR/"
cp "risk_customer_manage表业务流程分析-07-总结和建议.md" "$TEMP_DIR/"
cp "risk_customer_manage表业务流程分析-08-新人上手指南.md" "$TEMP_DIR/"

# 创建README文件
cat > "$TEMP_DIR/README.md" << 'EOF'
# risk_customer_manage表业务流程分析文档包

## 文档说明

本文档包包含了对car-risk项目中risk_customer_manage表的完整业务流程分析，适用于新加入的研发人员快速理解项目架构和业务逻辑。

## 文档结构

### 01 - 项目总览和架构
- 项目整体架构介绍
- 模块划分和职责
- 技术栈和部署架构
- 核心业务场景

### 02 - 数据访问层详细分析
- Mapper接口定义
- SQL实现分析
- 索引设计和性能优化
- 数据一致性保证

### 03-1 - car-risk-manage模块Service层
- 管理端业务逻辑
- CRUD操作流程
- Excel批量处理
- 数据校验和转换

### 03-2 - car-risk-process模块Service层
- 业务端处理逻辑
- 定时任务处理
- 数据同步机制
- 缓存管理策略

### 04 - Controller层接口详细分析
- HTTP接口定义
- 参数和响应格式
- 权限控制和安全
- 异常处理机制

### 05 - 规则引擎集成分析
- 规则引擎架构
- 风控检查流程
- 黑名单处理逻辑
- 性能优化策略

### 06 - 调用链路详细分析
- 完整调用链路
- 性能监控和追踪
- 异常处理和降级
- 数据流向分析

### 07 - 总结和建议
- 系统优势和问题
- 性能优化建议
- 架构改进方案
- 数据治理策略

### 08 - 新人上手指南
- 环境搭建指南
- 开发任务示例
- 调试和排查技巧
- 学习路径建议

## 使用建议

1. **新人入门**：建议按照文档编号顺序阅读，从01开始逐步深入
2. **问题排查**：遇到具体问题时，可直接查阅相关章节
3. **开发参考**：进行功能开发时，参考相应的代码分析和最佳实践
4. **架构理解**：需要了解整体架构时，重点阅读01、05、06章节

## 更新说明

- 文档基于当前代码版本分析生成
- 建议定期更新文档以保持与代码同步
- 如有疑问或发现问题，请及时反馈

## 联系方式

如有任何问题或建议，请联系项目维护团队。

---
文档生成时间: $(date)
分析范围: car-risk项目 risk_customer_manage表相关业务流程
EOF

# 创建目录结构说明
cat > "$TEMP_DIR/文档目录结构.md" << 'EOF'
# 文档目录结构

```
risk_customer_manage_analysis/
├── README.md                                           # 文档包说明
├── 文档目录结构.md                                      # 本文件
├── risk_customer_manage表业务流程分析-01-项目总览和架构.md
├── risk_customer_manage表业务流程分析-02-数据访问层详细分析.md
├── risk_customer_manage表业务流程分析-03-1-manage模块Service层.md
├── risk_customer_manage表业务流程分析-03-2-process模块Service层.md
├── risk_customer_manage表业务流程分析-04-Controller层接口详细分析.md
├── risk_customer_manage表业务流程分析-05-规则引擎集成分析.md
├── risk_customer_manage表业务流程分析-06-调用链路详细分析.md
├── risk_customer_manage表业务流程分析-07-总结和建议.md
└── risk_customer_manage表业务流程分析-08-新人上手指南.md
```

## 阅读顺序建议

### 快速了解（1-2小时）
1. README.md
2. 01-项目总览和架构.md
3. 08-新人上手指南.md

### 深入学习（1-2天）
1. 02-数据访问层详细分析.md
2. 03-1-manage模块Service层.md
3. 03-2-process模块Service层.md
4. 04-Controller层接口详细分析.md

### 高级理解（2-3天）
1. 05-规则引擎集成分析.md
2. 06-调用链路详细分析.md
3. 07-总结和建议.md

## 重点关注内容

### 新人必读
- 项目架构和模块划分
- 核心业务概念和枚举定义
- 基本的CRUD操作流程
- 开发环境搭建

### 开发重点
- 数据访问层的SQL优化
- Service层的业务逻辑处理
- Controller层的接口设计
- 异常处理和日志记录

### 架构理解
- 规则引擎的集成方式
- 缓存策略和性能优化
- 分布式锁和并发控制
- 监控和告警机制
EOF

# 创建ZIP文件
ZIP_NAME="risk_customer_manage表业务流程分析_$(date +%Y%m%d_%H%M%S).zip"
echo "创建ZIP文件: $ZIP_NAME"

if command -v zip &> /dev/null; then
    zip -r "$ZIP_NAME" "$TEMP_DIR"
else
    echo "zip命令不可用，使用tar创建压缩包..."
    tar -czf "${ZIP_NAME%.zip}.tar.gz" "$TEMP_DIR"
    ZIP_NAME="${ZIP_NAME%.zip}.tar.gz"
fi

# 清理临时目录
rm -rf "$TEMP_DIR"

echo "打包完成！"
echo "文件名: $ZIP_NAME"
echo "文件大小: $(ls -lh "$ZIP_NAME" | awk '{print $5}')"
echo ""
echo "文档包含以下内容："
echo "- 项目总览和架构分析"
echo "- 数据访问层详细分析"
echo "- Service层业务逻辑分析"
echo "- Controller层接口分析"
echo "- 规则引擎集成分析"
echo "- 调用链路详细分析"
echo "- 总结和优化建议"
echo "- 新人上手指南"
echo ""
echo "建议新加入的研发人员按照文档编号顺序阅读，快速上手项目开发。"
EOF
