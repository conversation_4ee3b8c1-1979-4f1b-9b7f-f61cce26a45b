# risk_customer_manage表业务流程分析 - 08 新人上手指南

## 1. 快速入门指南

### 1.1 项目概述理解

作为新加入的研发人员，首先需要理解car-risk项目的整体定位：

- **项目性质**：分布式风控系统，核心处理车辆相关业务的风险控制
- **核心表**：risk_customer_manage表是整个系统的数据核心
- **业务价值**：通过风控名单管理，保障平台业务安全

### 1.2 环境准备

#### 1.2.1 开发环境搭建
```bash
# 1. 克隆代码仓库
git clone [repository-url]
cd car-risk

# 2. 安装依赖
mvn clean install

# 3. 配置数据库连接
# 修改 application-dev.yml 中的数据库配置

# 4. 启动Redis
docker run -d --name redis -p 6379:6379 redis:latest

# 5. 启动应用
# 启动 car-risk-manage 模块
cd car-risk-manage
mvn spring-boot:run

# 启动 car-risk-process 模块  
cd car-risk-process
mvn spring-boot:run
```

#### 1.2.2 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE car_risk DEFAULT CHARACTER SET utf8mb4;

-- 创建核心表
CREATE TABLE risk_customer_manage (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    customer_type INT NOT NULL COMMENT '客户类型',
    customer_value VARCHAR(255) NOT NULL COMMENT '客户值',
    risk_type INT NOT NULL COMMENT '风险类型',
    status INT DEFAULT 1 COMMENT '状态',
    ttl INT COMMENT '有效期天数',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    invalid_time DATETIME COMMENT '失效时间',
    -- 其他字段...
);

-- 创建必要索引
CREATE INDEX idx_customer_value_type ON risk_customer_manage(customer_value, customer_type);
CREATE INDEX idx_invalid_time_status ON risk_customer_manage(invalid_time, status);
```

### 1.3 核心概念理解

#### 1.3.1 风控名单类型
```java
// 风险类型枚举
public enum RiskCustomerRiskTypeEnum {
    BLACK_LIST(1, "黑名单"),           // 完全禁止
    WHITE_LIST(2, "白名单"),           // 完全放行
    BAN_ONE_TO_ONE_LIST(7, "一对一禁止名单"), // 特定乘客与司机禁止匹配
    BAN_RECEIVE_LIST(6, "禁止接单名单"),      // 禁止接单但可以继续服务
    // 其他类型...
}
```

#### 1.3.2 客户类型分类
```java
// 客户类型枚举
public enum RiskCustomerCustomerTypeEnum {
    USER_ID(1, "用户ID"),
    USER_PHONE(2, "用户手机号"),
    DRIVER_CARD_NO(6, "司机车牌号"),
    USER_CERT_NO(10, "身份证号"),
    // 其他类型...
}
```

## 2. 代码结构导航

### 2.1 模块结构图
```
car-risk/
├── car-risk-manage/           # 管理端模块
│   ├── controller/           # HTTP接口层
│   │   └── RiskCustomerController.java
│   ├── service/             # 业务逻辑层
│   │   └── RiskCustomerService.java
│   └── repo/mapper/         # 数据访问层
│       └── RiskCustomerManageMapper.java
│
├── car-risk-process/         # 业务处理模块
│   ├── controller/          # HTTP接口层
│   │   ├── RiskCustomerController.java
│   │   └── ability/         # 风控能力接口
│   ├── service/            # 业务逻辑层
│   │   ├── RiskCustomerService.java
│   │   └── BlackListService.java
│   └── repo/mapper/        # 数据访问层
│       └── RiskCustomerManageMapper.java
│
└── car-risk-common/         # 公共组件
    └── enums/              # 枚举定义
```

### 2.2 关键文件说明

#### 2.2.1 核心业务文件
| 文件路径 | 功能说明 | 重要程度 |
|---------|---------|----------|
| `RiskCustomerController.java` | HTTP接口定义 | ⭐⭐⭐⭐⭐ |
| `RiskCustomerService.java` | 核心业务逻辑 | ⭐⭐⭐⭐⭐ |
| `RiskCustomerManageMapper.java` | 数据访问接口 | ⭐⭐⭐⭐⭐ |
| `RiskCustomerManageMapper.xml` | SQL实现 | ⭐⭐⭐⭐ |
| `BlackListService.java` | 黑名单处理逻辑 | ⭐⭐⭐⭐ |

#### 2.2.2 配置文件
| 文件路径 | 功能说明 |
|---------|---------|
| `application.yml` | 主配置文件 |
| `application-dev.yml` | 开发环境配置 |
| `logback-spring.xml` | 日志配置 |

## 3. 常见开发任务

### 3.1 新增风控类型

#### 3.1.1 步骤说明
1. **枚举定义**：在`RiskCustomerRiskTypeEnum`中添加新类型
2. **数据库支持**：确保数据库支持新的risk_type值
3. **业务逻辑**：在Service层添加新类型的处理逻辑
4. **接口适配**：更新Controller层的接口文档

#### 3.1.2 代码示例
```java
// 1. 枚举定义
public enum RiskCustomerRiskTypeEnum {
    // 现有类型...
    NEW_RISK_TYPE(9, "新风控类型", "新增的风控类型描述");
    
    private final Integer code;
    private final String message;
    private final String description;
}

// 2. Service层处理
@Service
public class RiskCustomerService {
    
    public Boolean add(RiskCustomerAddParams params) {
        // 新增类型的特殊处理逻辑
        if (params.getRiskType().equals(RiskCustomerRiskTypeEnum.NEW_RISK_TYPE.getCode())) {
            return handleNewRiskType(params);
        }
        
        // 原有逻辑...
    }
    
    private Boolean handleNewRiskType(RiskCustomerAddParams params) {
        // 新类型的特殊业务逻辑
        validateNewRiskTypeParams(params);
        
        // 执行插入逻辑
        return insertNewRecord(params);
    }
}
```

### 3.2 新增查询维度

#### 3.2.1 添加新的客户类型
```java
// 1. 枚举扩展
public enum RiskCustomerCustomerTypeEnum {
    // 现有类型...
    NEW_CUSTOMER_TYPE(11, "新客户类型");
}

// 2. Mapper XML扩展
<select id="getListByValueByGroup" resultType="com.ly.car.risk.entity.RiskCustomerManage">
    select * from risk_customer_manage where invalid_time &gt; #{invalidTime} and (
    <trim prefixOverrides="or">
        <!-- 现有查询条件... -->
        
        <!-- 新增查询条件 -->
        <if test="params.newCustomerField != null and params.newCustomerField != ''">
            or (customer_type = 11 and customer_value = #{params.newCustomerField})
        </if>
    </trim>
    )
</select>

// 3. 参数类扩展
public class CommonCustomerParam {
    // 现有字段...
    private String newCustomerField;
    
    // getter/setter...
}
```

### 3.3 性能优化任务

#### 3.3.1 添加新索引
```sql
-- 分析慢查询
EXPLAIN SELECT * FROM risk_customer_manage 
WHERE customer_value = 'xxx' 
  AND customer_type = 6 
  AND invalid_time > NOW();

-- 添加优化索引
CREATE INDEX idx_customer_value_type_invalid 
ON risk_customer_manage(customer_value, customer_type, invalid_time);
```

#### 3.3.2 缓存优化
```java
@Service
public class RiskCustomerCacheService {
    
    @Cacheable(value = "riskCustomer", key = "#customerValue + '_' + #customerType")
    public List<RiskCustomerManage> getCachedRiskList(String customerValue, Integer customerType) {
        return riskCustomerManageMapper.selectList(
            new QueryWrapper<RiskCustomerManage>()
                .eq("customer_value", customerValue)
                .eq("customer_type", customerType)
                .eq("status", 1)
                .gt("invalid_time", new Date())
        );
    }
    
    @CacheEvict(value = "riskCustomer", key = "#entity.customerValue + '_' + #entity.customerType")
    public void evictCache(RiskCustomerManage entity) {
        // 缓存失效逻辑
    }
}
```

## 4. 调试和问题排查

### 4.1 常见问题及解决方案

#### 4.1.1 数据查询问题
```java
// 问题：查询结果为空
// 排查步骤：
// 1. 检查查询条件
log.info("查询参数：{}", JsonUtils.json(params));

// 2. 检查数据状态
SELECT * FROM risk_customer_manage 
WHERE customer_value = 'xxx' 
  AND status = 1 
  AND invalid_time > NOW();

// 3. 检查缓存状态
String cacheKey = buildCacheKey(customerValue, customerType);
Object cachedValue = redisTemplate.opsForValue().get(cacheKey);
log.info("缓存值：{}", cachedValue);
```

#### 4.1.2 性能问题排查
```java
// 添加性能监控
@Around("execution(* com.ly.car.risk.*.service.*.*(..))")
public Object monitorPerformance(ProceedingJoinPoint joinPoint) throws Throwable {
    long startTime = System.currentTimeMillis();
    String methodName = joinPoint.getSignature().getName();
    
    try {
        Object result = joinPoint.proceed();
        long endTime = System.currentTimeMillis();
        
        if (endTime - startTime > 1000) {
            log.warn("慢方法告警：{}，耗时：{}ms", methodName, endTime - startTime);
        }
        
        return result;
    } catch (Exception e) {
        log.error("方法执行异常：{}", methodName, e);
        throw e;
    }
}
```

### 4.2 日志分析技巧

#### 4.2.1 关键日志位置
```bash
# 应用日志
tail -f logs/car-risk-manage.log | grep "RiskCustomer"

# 数据库慢查询日志
tail -f /var/log/mysql/slow.log

# Redis日志
redis-cli monitor | grep "risk_customer"
```

#### 4.2.2 日志分析示例
```bash
# 查找特定操作的完整链路
grep "traceId:12345" logs/*.log | sort

# 统计接口调用频率
grep "POST /riskListManage/riskCustomer" logs/access.log | 
awk '{print $4}' | cut -d: -f1-2 | sort | uniq -c

# 查找异常信息
grep -A 10 -B 5 "Exception" logs/car-risk-manage.log
```

## 5. 测试指南

### 5.1 单元测试

#### 5.1.1 Service层测试
```java
@SpringBootTest
@Transactional
@Rollback
public class RiskCustomerServiceTest {
    
    @Autowired
    private RiskCustomerService riskCustomerService;
    
    @Test
    public void testAddRiskCustomer() {
        // 准备测试数据
        RiskCustomerAddParams params = new RiskCustomerAddParams();
        params.setCustomerType(RiskCustomerCustomerTypeEnum.DRIVER_CARD_NO.getCode());
        params.setCustomerValue("测试车牌001");
        params.setRiskType(RiskCustomerRiskTypeEnum.BLACK_LIST.getCode());
        params.setTtl(30);
        params.setRiskRemark("测试拉黑");
        
        // 执行测试
        Boolean result = riskCustomerService.add(params);
        
        // 验证结果
        assertTrue(result);
        
        // 验证数据库
        RiskCustomerManage entity = riskCustomerManageMapper.getByTypeAndValueAndRiskType(
            params.getCustomerType(), params.getCustomerValue(), params.getRiskType(), new Date());
        assertNotNull(entity);
        assertEquals("测试拉黑", entity.getRiskRemark());
    }
}
```

#### 5.1.2 Mapper层测试
```java
@MybatisTest
public class RiskCustomerManageMapperTest {
    
    @Autowired
    private RiskCustomerManageMapper mapper;
    
    @Test
    public void testSelectByCondition() {
        // 插入测试数据
        RiskCustomerManage entity = createTestEntity();
        mapper.insert(entity);
        
        // 测试查询
        List<RiskCustomerManage> result = mapper.selectList(
            new QueryWrapper<RiskCustomerManage>()
                .eq("customer_value", "测试车牌001")
        );
        
        assertEquals(1, result.size());
        assertEquals("测试车牌001", result.get(0).getCustomerValue());
    }
}
```

### 5.2 集成测试

#### 5.2.1 接口测试
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
public class RiskCustomerControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    public void testAddRiskCustomer() {
        // 准备请求数据
        RiskCustomerAddParams params = new RiskCustomerAddParams();
        params.setCustomerType(6);
        params.setCustomerValue("测试车牌001");
        params.setRiskType(1);
        params.setTtl(30);
        
        // 发送请求
        ResponseEntity<Boolean> response = restTemplate.postForEntity(
            "/riskListManage/riskCustomer/add", params, Boolean.class);
        
        // 验证响应
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody());
    }
}
```

### 5.3 性能测试

#### 5.3.1 压力测试脚本
```bash
#!/bin/bash
# 风控查询接口压力测试

# 测试参数
URL="http://localhost:8080/customer/queryAll"
CONCURRENT=50
REQUESTS=1000

# 准备测试数据
cat > test_data.json << EOF
{
    "plateNumber": "测试车牌001",
    "passengerPhone": "13800138000"
}
EOF

# 执行压力测试
ab -n $REQUESTS -c $CONCURRENT -p test_data.json -T application/json $URL

# JMeter测试
jmeter -n -t risk_customer_test.jmx -l result.jtl
```

## 6. 部署和运维

### 6.1 部署流程

#### 6.1.1 构建和打包
```bash
# 1. 代码构建
mvn clean package -DskipTests

# 2. Docker镜像构建
docker build -t car-risk-manage:latest ./car-risk-manage
docker build -t car-risk-process:latest ./car-risk-process

# 3. 推送到镜像仓库
docker push registry.company.com/car-risk-manage:latest
docker push registry.company.com/car-risk-process:latest
```

#### 6.1.2 部署配置
```yaml
# docker-compose.yml
version: '3.8'
services:
  car-risk-manage:
    image: registry.company.com/car-risk-manage:latest
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql-server
      - REDIS_HOST=redis-server
    depends_on:
      - mysql
      - redis
      
  car-risk-process:
    image: registry.company.com/car-risk-process:latest
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql-server
      - REDIS_HOST=redis-server
    depends_on:
      - mysql
      - redis
```

### 6.2 监控配置

#### 6.2.1 应用监控
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'car-risk-manage'
    static_configs:
      - targets: ['car-risk-manage:8080']
    metrics_path: '/actuator/prometheus'
    
  - job_name: 'car-risk-process'
    static_configs:
      - targets: ['car-risk-process:8081']
    metrics_path: '/actuator/prometheus'
```

#### 6.2.2 告警规则
```yaml
# alert_rules.yml
groups:
  - name: car-risk-alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          
      - alert: SlowResponse
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Slow response time detected"
```

## 7. 学习路径建议

### 7.1 第一周：基础理解
- [ ] 阅读项目文档，理解业务背景
- [ ] 搭建开发环境，成功启动项目
- [ ] 熟悉数据库表结构和核心字段含义
- [ ] 理解各种风控类型的业务含义

### 7.2 第二周：代码熟悉
- [ ] 阅读Controller层代码，理解接口定义
- [ ] 阅读Service层代码，理解业务逻辑
- [ ] 阅读Mapper层代码，理解数据访问
- [ ] 运行单元测试，理解测试用例

### 7.3 第三周：实践操作
- [ ] 完成一个简单的功能开发（如新增查询条件）
- [ ] 编写对应的单元测试
- [ ] 进行代码Review，学习最佳实践
- [ ] 参与线上问题排查

### 7.4 第四周：深入理解
- [ ] 理解缓存策略和性能优化
- [ ] 学习分布式锁和并发控制
- [ ] 了解监控和告警机制
- [ ] 参与系统设计讨论

## 8. 常用工具和命令

### 8.1 开发工具
```bash
# Git常用命令
git checkout -b feature/new-feature
git add .
git commit -m "feat: 添加新功能"
git push origin feature/new-feature

# Maven命令
mvn clean compile                # 编译
mvn clean test                   # 运行测试
mvn clean package               # 打包
mvn dependency:tree             # 查看依赖树

# Docker命令
docker ps                       # 查看运行容器
docker logs container_name      # 查看容器日志
docker exec -it container_name bash  # 进入容器
```

### 8.2 数据库工具
```sql
-- 常用查询
-- 查看表结构
DESCRIBE risk_customer_manage;

-- 查看索引
SHOW INDEX FROM risk_customer_manage;

-- 查看执行计划
EXPLAIN SELECT * FROM risk_customer_manage WHERE customer_value = 'xxx';

-- 查看慢查询
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;
```

### 8.3 Redis工具
```bash
# Redis命令
redis-cli                       # 连接Redis
KEYS risk_customer:*            # 查看相关缓存
GET risk_customer:key           # 获取缓存值
DEL risk_customer:key           # 删除缓存
MONITOR                         # 监控Redis操作
```

这个新人上手指南为新加入的研发人员提供了完整的学习路径和实践指导，确保能够快速理解项目并参与开发工作。
