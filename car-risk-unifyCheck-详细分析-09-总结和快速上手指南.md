# car-risk项目 `/riskCheck/unifyCheck` 接口详细分析 - 第9部分：总结和快速上手指南

## 1. 项目总体架构总结

### 1.1 核心处理流程

unifyCheck接口采用分层处理架构，确保风控检查的全面性和高效性：

```
HTTP请求 → 参数校验 → 黑名单检查 → 特殊场景检查 → 策略检查 → 结果返回
```

### 1.2 关键组件说明

| 组件 | 职责 | 核心类 |
|------|------|--------|
| 控制器层 | 接收HTTP请求，统一异常处理 | RiskCheckController |
| 黑名单检查 | 检查各类风控名单 | BlackListHandler, CommonBlackListHandler |
| 特殊场景处理 | 处理特定业务场景逻辑 | SpecialSceneHandlerFactory |
| 策略检查 | 执行动态配置的风控策略 | RiskStrategyHandler, RiskStrategyHelper |
| 数据访问层 | 数据库操作 | RiskCustomerService, RiskHitService |

### 1.3 支持的业务场景

| 场景编码 | 场景名称 | 主要检查内容 |
|---------|---------|-------------|
| 1-1 | 司机注册 | 设备黑名单、注册频率 |
| 2-1 | 用户下单 | 用户黑名单、下单频率 |
| 2-2 | 用户派单 | 用户黑名单、派单限制 |
| 5-1 | 司机接单 | 车牌黑名单、一对一限制、接单频率 |
| 5-2 | 调度员接单 | 调度员黑名单、接单限制 |
| 6-1 | 司机认证 | 身份证黑名单、认证频率 |
| 7-1 | 银行卡变更 | 变更频率、风险账户 |
| 8-1 | 订单完成 | 完单异常检测 |
| 9-1 | 活动领券 | 领券黑名单、领券频率 |
| 10-1 | 取消提醒 | 取消频率检测 |
| 11-1 | 权益订单 | 权益滥用检测 |

## 2. 核心数据库表总结

### 2.1 风控名单表

**risk_customer_manage**: 存储所有类型的风控名单
- 支持多种客户类型：用户ID、手机号、车牌号、设备ID等
- 支持多种风险类型：黑名单、白名单、一对一名单等
- 支持TTL机制，自动失效

### 2.2 策略配置表

**metric_strategy**: 策略主表
**metric_rule**: 规则表  
**metric_field**: 指标表
**关联表**: 场景-策略、策略-规则、规则-指标关联关系

### 2.3 命中记录表

**risk_hit**: 记录所有风控命中信息，用于分析和审计

## 3. 新人快速上手指南

### 3.1 环境准备

#### 3.1.1 开发环境要求

```bash
# Java环境
Java 8+
Maven 3.6+

# 数据库
MySQL 5.7+
Redis 3.0+

# IDE推荐
IntelliJ IDEA
```

#### 3.1.2 项目启动

```bash
# 1. 克隆代码
git clone [项目地址]

# 2. 导入IDE
# 使用IntelliJ IDEA导入Maven项目

# 3. 配置数据库连接
# 修改application.yml中的数据库配置

# 4. 启动项目
mvn spring-boot:run
```

### 3.2 接口调用示例

#### 3.2.1 司机接单场景

```bash
curl -X POST http://localhost:8080/riskCheck/unifyCheck \
-H "Content-Type: application/json" \
-d '{
    "scene": "5-1",
    "productLine": "YNC",
    "channel": "APP",
    "carNum": "京A12345",
    "passengerPhone": "13800138000",
    "orderId": "YNC202412260001",
    "supplierCode": "SUPPLIER001",
    "traceId": "trace-123456789"
}'
```

#### 3.2.2 用户下单场景

```bash
curl -X POST http://localhost:8080/riskCheck/unifyCheck \
-H "Content-Type: application/json" \
-d '{
    "scene": "2-1",
    "productLine": "SFC",
    "channel": "H5",
    "memberId": "123456789",
    "userPhone": "13900139000",
    "ip": "*************",
    "traceId": "trace-987654321"
}'
```

### 3.3 常见开发任务

#### 3.3.1 新增业务场景

**步骤1**: 在StrategySceneEnum中添加新场景
```java
public enum StrategySceneEnum {
    // 现有场景...
    NEW_SCENE("12-1", "新业务场景");
}
```

**步骤2**: 在参数校验中添加场景特定校验
```java
private void paramCheck(UnifyCheckRequest request) {
    // 现有校验...
    switch (sceneEnum) {
        case NEW_SCENE:
            CheckUtil.notBlankCheck(request.getSpecialParam(), "特殊参数不可为空");
            break;
    }
}
```

**步骤3**: 如需特殊处理，创建场景处理器
```java
@Component
public class NewSceneHandler implements SpecialSceneHandler {
    
    @Override
    public List<StrategySceneEnum> supportScene() {
        return Arrays.asList(StrategySceneEnum.NEW_SCENE);
    }
    
    @Override
    public RiskSceneResult check(UnifyCheckRequest request) {
        // 特殊场景检查逻辑
        return null;
    }
}
```

#### 3.3.2 新增风控策略

**步骤1**: 在管理后台配置策略
- 策略基本信息：名称、场景、业务线等
- 规则配置：规则表达式
- 指标配置：指标计算脚本

**步骤2**: 编写指标脚本
```groovy
// 指标脚本示例：计算用户当日下单次数
def check(data) {
    def memberId = data.get("memberId")
    def today = new Date().format("yyyy-MM-dd")
    
    // 调用数据查询服务
    def count = riskDataService.getUserOrderCountByDate(memberId, today)
    
    return ["num": count]
}
```

**步骤3**: 编写规则脚本
```groovy
// 规则脚本示例：检查下单次数是否超限
def check(data) {
    def orderCount = data.get("field1001")
    return orderCount > 10
}
```

**步骤4**: 编写策略脚本
```groovy
// 策略脚本示例：综合判断
def check(data) {
    def rule1 = data.get("rule1001")  // 下单次数规则
    def rule2 = data.get("rule1002")  // 其他规则
    
    return rule1 && rule2
}
```

#### 3.3.3 新增黑名单类型

**步骤1**: 在RiskCustomerRiskTypeEnum中添加新类型
```java
public enum RiskCustomerRiskTypeEnum {
    // 现有类型...
    new_risk_type(11, "新风险类型");
}
```

**步骤2**: 在场景风险类型映射中添加配置
```java
public static List<Integer> sceneRiskType(String scene, String productLine) {
    // 现有映射...
    if (Objects.equals(scene, "12-1")) {
        return Arrays.asList(1, 11); // 黑名单 + 新风险类型
    }
}
```

### 3.4 调试和排查指南

#### 3.4.1 日志查看

```bash
# 查看接口调用日志
grep "unifyCheck" /path/to/logs/application.log

# 查看策略执行日志
grep "策略执行结果" /path/to/logs/application.log

# 查看异常日志
grep "ERROR" /path/to/logs/application.log
```

#### 3.4.2 常见问题排查

**问题1**: 接口返回"场景值异常"
- **原因**: 传入的scene参数不在StrategySceneEnum中
- **解决**: 检查scene参数是否正确

**问题2**: 策略不生效
- **原因**: 策略状态为测试状态或已停用
- **解决**: 检查metric_strategy表中的status字段

**问题3**: 黑名单检查不生效
- **原因**: 名单已过期或状态异常
- **解决**: 检查risk_customer_manage表中的status和invalid_time字段

#### 3.4.3 性能监控

```java
// 查看策略加载情况
LoggerUtils.info(log,"策略初始化结束，初始化策略条数：{}",riskStrategy.size());

// 查看接口响应时间
// 在日志中搜索请求开始和结束时间
```

## 4. 最佳实践建议

### 4.1 开发规范

1. **参数校验**: 新增场景时必须添加相应的参数校验
2. **异常处理**: 所有外部调用都要有异常处理
3. **日志记录**: 关键节点要记录详细日志
4. **单元测试**: 新增功能要编写单元测试

### 4.2 性能优化

1. **缓存使用**: 合理使用Redis缓存减少数据库查询
2. **异步处理**: 非关键路径使用异步处理
3. **批量操作**: 避免循环调用数据库
4. **索引优化**: 确保查询字段有合适的索引

### 4.3 运维监控

1. **接口监控**: 监控接口响应时间和成功率
2. **业务监控**: 监控各场景的命中率
3. **异常告警**: 设置异常告警机制
4. **容量规划**: 定期评估系统容量

## 5. 联系方式和资源

### 5.1 技术支持

- **项目负责人**: [负责人姓名]
- **技术群**: [技术交流群]
- **文档地址**: [项目文档地址]

### 5.2 相关资源

- **代码仓库**: [Git仓库地址]
- **API文档**: [接口文档地址]  
- **监控大盘**: [监控系统地址]
- **配置中心**: [配置管理地址]

### 5.3 学习资源

- **Groovy语法**: [Groovy官方文档]
- **Spring Boot**: [Spring Boot官方文档]
- **MyBatis**: [MyBatis官方文档]

通过本文档的详细分析，新加入的研发同学可以快速理解car-risk项目的unifyCheck接口架构、实现原理和开发流程，为后续的业务需求迭代奠定坚实的基础。

---

**文档版本**: v1.0  
**最后更新**: 2024-12-26  
**文档作者**: AI助手  
**审核状态**: 待审核
