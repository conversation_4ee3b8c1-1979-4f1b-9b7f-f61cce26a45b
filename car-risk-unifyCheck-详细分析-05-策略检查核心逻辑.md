# car-risk项目 `/riskCheck/unifyCheck` 接口详细分析 - 第5部分：策略检查核心逻辑

## 1. 策略检查总体架构

### 1.1 策略检查在整体流程中的位置

策略检查是unifyCheck接口的核心环节，位于黑名单检查和特殊场景检查之后：

```java
public RiskSceneResult unifyCheck(UnifyCheckRequest request) throws BizException {
    // 前置检查...
    
    // 策略检查前的一些动作
    beforeStrategyCheck(request);

    // 策略检查 - 核心逻辑
    RiskSceneResult riskSceneResult = strategyCheck(request);

    // 策略检查后的一些动作
    afterStrategyCheck(request, riskSceneResult);

    return riskSceneResult;
}
```

### 1.2 策略检查核心流程

```java
public RiskSceneResult strategyCheck(UnifyCheckRequest request) {
    Map<String, Object> paramMap = fillParam(request);
    
    try {
        // 1. 构建上下文
        StrategyContext strategyContext = buildContext(paramMap, request);
        
        // 2. 对入参一些字段进行加密处理
        encryptParam(strategyContext);
        
        // 3. 查找策略
        findStrategy(strategyContext);
        
        // 4. 自检指标（填充订单参数）
        fillOrderParam(strategyContext);
        
        // 5. 执行策略
        strategyHandle(strategyContext);
        
        // 6. 针对策略的后置处理
        strategyPostProcessing(strategyContext);
        
        return strategyContext.getResult();
    } finally {
        // 清理本地缓存
        localCache.remove();
        localObjectCache.remove();
    }
}
```

## 2. 策略上下文构建

### 2.1 参数填充

```java
private Map<String, Object> fillParam(UnifyCheckRequest request) {
    Map<String, Object> params = null == request.getExt() ? new HashMap<>() : request.getExt();
    
    // 基础参数
    params.put(SCENE, request.getScene());
    params.put(MEMBER_ID, request.getMemberId());
    params.put(UNION_ID, request.getUnionId());
    params.put(CHANNEL, request.getChannel());
    params.put(PRODUCT_LINE, request.getProductLine());
    params.put(ORDER_ID, request.getOrderId());
    
    // 用户相关参数
    params.put(USER_PHONE, request.getUserPhone());
    params.put(PASSENGER_PHONE, request.getPassengerPhone());
    
    // 司机相关参数
    params.put(CAR_NUM, request.getCarNum());
    
    // 其他参数
    params.put(IP, request.getIp());
    params.put(SUPPLIER_CODE, request.getSupplierCode());
    params.put(SUPPLIER_NAME, request.getSupplierName());
    params.put(DEVICE_ID, request.getDeviceId());
    
    return params;
}
```

### 2.2 上下文构建

```java
private StrategyContext buildContext(Map<String, Object> params, UnifyCheckRequest request) {
    StrategyContext strategyContext = new StrategyContext();
    strategyContext.setParams(params);
    strategyContext.setRequest(request);
    return strategyContext;
}
```

### 2.3 StrategyContext模型

```java
@Data
public class StrategyContext {
    // 请求参数
    private Map<String, Object> params;
    
    // 原始请求对象
    private UnifyCheckRequest request;
    
    // 匹配的策略列表
    private List<RiskStrategyDetail> strategyList;
    
    // 策略执行结果
    private Map<String, RiskStrategyResult> strategyResultMap;
    
    // 最终结果
    private RiskSceneResult result;
}
```

## 3. 策略查找逻辑

### 3.1 策略查找入口

```java
public void findStrategy(StrategyContext context) {
    Map<String, Object> params = context.getParams();
    
    // 获取查找条件
    String productLine = (String) params.get(PRODUCT_LINE);
    String channel = null == params.get(CHANNEL) ? StringUtils.EMPTY : (String) params.get(CHANNEL);
    String scene = (String) params.get(SCENE);
    String supplierCode = (String) params.get(SUPPLIER_CODE);
    
    // 查找匹配的策略
    List<RiskStrategyDetail> strategyList = strategyHelper.findStrategy(productLine, channel, scene, supplierCode);
    
    String strategyNos = strategyList.stream().map(RiskStrategyDetail::getStrategyNo).collect(Collectors.joining(","));
    LoggerUtils.info(log, "获取到对应策略:{} ", strategyNos);
    
    context.setStrategyList(strategyList);
}
```

### 3.2 RiskStrategyHelper策略查找

**文件位置**: `car-risk-process/src/main/java/com/ly/car/risk/process/strategy/RiskStrategyHelper.java`

```java
public List<RiskStrategyDetail> findStrategy(String productLine, String channel, String scene, String supplierCode) {
    // 基础过滤：业务线、渠道、场景
    List<RiskStrategyDetail> strategyList = riskStrategy.stream()
            .filter(p -> p.getProductLine().contains(productLine)
                    && (StringUtils.isBlank(channel) || p.getChannels().contains(channel) || p.getChannels().contains("all"))
                    && Objects.equals(p.getScene(), scene))
            .collect(Collectors.toList());
    
    // 供应商为空不过滤
    if (StringUtils.isBlank(supplierCode)) {
        return strategyList;
    }
    
    // 精准过滤出策略才执行
    List<RiskStrategyDetail> supplierStrategylist = strategyList.stream()
            .filter(s -> s.getSupplierCodes().contains(supplierCode))
            .collect(Collectors.toList());
    
    if (CollectionUtils.isNotEmpty(supplierStrategylist)) {
        return supplierStrategylist;
    }
    
    return strategyList;
}
```

### 3.3 策略数据加载机制

```java
@Override
public void afterPropertiesSet() throws Exception {
    ScheduledExecutorService executorService = new ScheduledThreadPoolExecutor(1);
    // 每分钟刷新一次策略数据
    executorService.scheduleWithFixedDelay(() -> initRiskStrategy(), 0, 1, TimeUnit.MINUTES);
}

private void initRiskStrategy() {
    LoggerUtils.initLogMap("STRATEGY_INIT", "", "", "");
    List<RiskStrategyDetail> riskStrategyList = new ArrayList<>();
    try {
        // 1. 查询场景策略关系
        List<RiskSceneStrategyDTO> sceneStrategyList = sceneStrategyRelationMapper.findSceneStrategy();
        
        // 2. 查询策略规则关系
        List<RiskStrategyRuleDTO> strategyRuleList = sceneStrategyRelationMapper.findStrategyRule();
        Map<Long, List<RiskStrategyRuleDTO>> strategyRuleMap = strategyRuleList.stream()
                .collect(Collectors.groupingBy(RiskStrategyRuleDTO::getStrategyId));
        
        // 3. 查询规则指标关系
        Map<Long, List<RiskRuleFieldDTO>> ruleFieldMap = sceneStrategyRelationMapper.findRuleField().stream()
                .collect(Collectors.groupingBy(RiskRuleFieldDTO::getRuleId));
        
        // 4. 查询指标详情
        Map<Long, MetricField> fieldMap = fieldMapper.selectList(new QueryWrapper<>()).stream()
                .collect(Collectors.toMap(p -> p.getId(), v -> v, (k1, k2) -> k1));

        // 5. 组装策略详情
        for (RiskSceneStrategyDTO sceneStrategy : sceneStrategyList) {
            RiskStrategyDetail riskStrategy = new RiskStrategyDetail();
            riskStrategyList.add(riskStrategy);
            
            // 策略基础信息
            BeanUtils.copyProperties(sceneStrategy, riskStrategy);
            riskStrategy.setProductLine(StrUtil.split(sceneStrategy.getProductLines(), ",", true, true));
            riskStrategy.setChannels(StrUtil.split(sceneStrategy.getChannels(), ",", true, true));
            riskStrategy.setSupplierCodes(StrUtil.split(sceneStrategy.getSupplierCodes(), ",", true, true));
            
            // 组装规则信息
            List<RiskRuleDetail> ruleDetails = new ArrayList<>();
            riskStrategy.setRules(ruleDetails);
            
            List<RiskStrategyRuleDTO> ruleList = strategyRuleMap.get(sceneStrategy.getStrategyId());
            if(CollUtil.isEmpty(ruleList)){
                continue;
            }
            
            for (RiskStrategyRuleDTO strategyRule : ruleList) {
                RiskRuleDetail riskRuleDetail = new RiskRuleDetail();
                ruleDetails.add(riskRuleDetail);
                BeanUtils.copyProperties(strategyRule, riskRuleDetail);
                
                // 组装指标信息
                List<RiskRuleFieldDTO> ruleFieldList = ruleFieldMap.get(strategyRule.getRuleId());
                if (CollUtil.isEmpty(ruleFieldList)) {
                    continue;
                }
                
                List<RiskFieldDetail> riskFields = ruleFieldList.stream().map(ruleField -> {
                    RiskFieldDetail fieldDetail = new RiskFieldDetail();
                    fieldDetail.setFieldId(String.valueOf(ruleField.getField()));
                    fieldDetail.setOperator(ruleField.getOperator());
                    fieldDetail.setRightType(ruleField.getRightType());
                    fieldDetail.setRightValue(ruleField.getRightValue());
                    fieldDetail.setSort(ruleField.getSort());
                    
                    MetricField metricField = fieldMap.get(ruleField.getField());
                    if (null != metricField) {
                        fieldDetail.setFieldNo(metricField.getFieldNo());
                        fieldDetail.setScript(metricField.getScript());
                        fieldDetail.setCategory(metricField.getCategory());
                        fieldDetail.setType(metricField.getType());
                        fieldDetail.setTarget(metricField.getTarget());
                        fieldDetail.setBasedCurrent(metricField.getBasedCurrent());
                    }
                    return fieldDetail;
                }).collect(Collectors.toList());
                
                riskRuleDetail.setRiskFields(riskFields);
            }
        }

        // 更新策略缓存
        riskStrategy = new CopyOnWriteArrayList<>(riskStrategyList);

    } catch (Exception ex) {
        LoggerUtils.error(log, "策略初始化 异常", ex);
    } finally {
        LoggerUtils.info(log,"策略初始化结束，初始化策略条数：{}",riskStrategy.size());
        LoggerUtils.removeAll();
    }
}
```

## 4. 订单参数填充

### 4.1 fillOrderParam逻辑

```java
private void fillOrderParam(StrategyContext strategyContext) {
    Map<String, Object> params = strategyContext.getParams();
    
    // 指标需要对当前订单校验，补充订单详情
    // 需要限制调用次数：目前仅用于8-1（订单完成场景）
    if (CollectionUtils.isNotEmpty(strategyContext.getStrategyList())
            && StringUtils.isNotBlank((String) params.get(ORDER_ID))
            && Objects.equals(StrategySceneEnum.FINISH_ORDER.getScene(), strategyContext.getRequest().getScene())) {
        
        // 检查是否有基于当前订单的指标
        boolean basedOnCurrent = strategyContext.getStrategyList().stream()
                .anyMatch(s -> s.getRules().stream()
                        .anyMatch(r -> r.getRiskFields().stream()
                                .anyMatch(f -> Objects.equals(f.getBasedCurrent(), 1))));
        
        if (basedOnCurrent) {
            CarOrderDetail orderDetail = carOrderService.queryOrderDetail((String) params.get(ORDER_ID));
            params.put(ORDER, orderDetail);
        }
    }
}
```

## 5. 策略执行逻辑

### 5.1 策略执行入口

```java
private void strategyHandle(StrategyContext context) {
    List<RiskStrategyDetail> strategyList = context.getStrategyList();
    if (CollUtil.isEmpty(strategyList)) {
        return;
    }
    
    Map<String, RiskStrategyResult> strategyResultMap = new HashMap<>();
    for (RiskStrategyDetail strategyDetail : strategyList) {
        RiskStrategyResult simpleStrategyResult = checkStrategy(strategyDetail, context.getParams());
        strategyResultMap.put(strategyDetail.getStrategyNo(), simpleStrategyResult);
    }
    
    LoggerUtils.info(log, "策略执行结果:{}", JSON.toJSONString(strategyResultMap));
    context.setStrategyResultMap(strategyResultMap);
}
```

### 5.2 单个策略检查

```java
private RiskStrategyResult checkStrategy(RiskStrategyDetail strategyDetail, Map<String, Object> params) {
    String strategyScript = strategyDetail.getScript();
    List<RiskRuleDetail> rules = strategyDetail.getRules();
    
    if (CollUtil.isEmpty(rules)) {
        return RiskStrategyResult.builder().strategyMatched(false).build();
    }
    
    Map<String, Object> ruleDataMap = new HashMap<>();
    List<String> matchRules = new ArrayList<>();
    
    // 1. 执行所有规则检查
    for (RiskRuleDetail rule : rules) {
        RiskRuleResult simpleRuleResult = checkRule(rule, params);
        LoggerUtils.info(log, "规则:{} 执行结果为:{}", rule.getRuleNo(), simpleRuleResult.getRuleMatched());
        
        // 收集规则结果
        ruleDataMap.put("rule" + rule.getRuleId(), simpleRuleResult.getRuleMatched());
        if (simpleRuleResult.getRuleMatched()) {
            matchRules.add(simpleRuleResult.getRuleNo());
        }
    }
    
    // 2. 执行策略脚本
    boolean strategyMatched = checkStrategyScript(strategyScript, ruleDataMap, strategyDetail.getStrategyNo());
    
    return RiskStrategyResult.builder()
            .strategyId(strategyDetail.getStrategyId())
            .strategyNo(strategyDetail.getStrategyNo())
            .strategyMatched(strategyMatched)
            .matchRules(matchRules).build();
}
```

### 5.3 规则检查逻辑

```java
private RiskRuleResult checkRule(RiskRuleDetail rule, Map<String, Object> params) {
    String ruleScript = rule.getScript();
    List<RiskFieldDetail> riskFields = rule.getRiskFields();
    Map<String, Object> fieldDataMap = new HashMap<>();
    
    // 1. 执行所有指标计算
    for (RiskFieldDetail riskField : riskFields) {
        String fieldScript = riskField.getScript();
        // 通过groovy脚本，从策略中获取值
        HashMap<String, Object> result = getFieldScriptResult(fieldScript, params, riskField.getFieldNo());
        String num = Optional.ofNullable(result.get("num")).map(String::valueOf).orElse("");
        
        double value;
        if (StringUtils.isBlank(num)) {
            value = 0.0;
        } else {
            value = new BigDecimal(num).setScale(2, RoundingMode.HALF_UP).doubleValue();
        }
        fieldDataMap.put("field" + riskField.getFieldId(), value);
    }
    
    // 2. 执行规则脚本
    boolean ruleResult = checkRuleScript(ruleScript, fieldDataMap, rule.getRuleNo());
    return RiskRuleResult.builder()
            .ruleId(rule.getRuleId())
            .ruleNo(rule.getRuleNo())
            .ruleMatched(ruleResult).build();
}
```

## 6. Groovy脚本执行

### 6.1 策略脚本执行

```java
private Boolean checkStrategyScript(String strategyScript, Map data, String strategyNo) {
    Object[] args = { data };
    Boolean ret = false;
    try {
        ret = (Boolean) GroovyScriptUtil.invokeMethod(strategyScript, "check", args);
    } catch (Exception e) {
        LoggerUtils.warn(log, "执行策略脚本失败,strategyNo:{}", e, strategyNo);
    }
    return ret;
}
```

### 6.2 规则脚本执行

```java
private Boolean checkRuleScript(String ruleScript, Map data, String ruleNo) {
    Object[] args = { data };
    Boolean ret = false;
    try {
        ret = (Boolean) GroovyScriptUtil.invokeMethod(ruleScript, "check", args);
    } catch (Exception e) {
        LoggerUtils.warn(log, "执行规则脚本失败,ruleNo:{}", e, ruleNo);
    }
    return ret;
}
```

### 6.3 指标脚本执行

```java
private HashMap<String, Object> getFieldScriptResult(String ruleScript, Map data, String fieldNo) {
    Object[] args = { data };
    HashMap<String, Object> ret = null;
    try {
        ret = (HashMap<String, Object>) GroovyScriptUtil.invokeMethod(ruleScript, "check", args);
    } catch (Exception e) {
        LoggerUtils.error(log, "执行指标脚本失败,fieldNo:{}", e, fieldNo);
        ret = new HashMap<>();
    }
    return ret;
}
```

## 7. 策略执行流程图

```mermaid
graph TD
    A[开始策略检查] --> B[填充请求参数]
    B --> C[构建策略上下文]
    C --> D[参数加密处理]
    D --> E[查找匹配策略]
    E --> F{是否有匹配策略?}
    F -->|否| G[返回通过结果]
    F -->|是| H[填充订单参数]
    H --> I[遍历策略列表]
    
    I --> J[执行单个策略检查]
    J --> K[遍历策略中的规则]
    K --> L[执行单个规则检查]
    L --> M[遍历规则中的指标]
    M --> N[执行指标脚本计算]
    N --> O{是否还有其他指标?}
    O -->|是| M
    O -->|否| P[执行规则脚本判断]
    P --> Q{是否还有其他规则?}
    Q -->|是| K
    Q -->|否| R[执行策略脚本判断]
    R --> S{是否还有其他策略?}
    S -->|是| I
    S -->|否| T[策略后置处理]
    T --> U[返回最终结果]
    
    subgraph "指标计算"
        V[获取指标脚本]
        W[执行Groovy脚本]
        X[返回计算结果]
    end
    
    subgraph "规则判断"
        Y[收集所有指标结果]
        Z[执行规则脚本]
        AA[返回规则匹配结果]
    end
    
    subgraph "策略判断"
        BB[收集所有规则结果]
        CC[执行策略脚本]
        DD[返回策略匹配结果]
    end
    
    N --> V
    P --> Y
    R --> BB
```

## 8. 本地缓存机制

### 8.1 ThreadLocal缓存

```java
private static final ThreadLocal<HashMap<String, List<CarRiskOrderDetail>>> localCache = ThreadLocal.withInitial(() -> new HashMap<>());
private static final ThreadLocal<HashMap<String, Object>> localObjectCache = ThreadLocal.withInitial(() -> new HashMap<>());

public List<CarRiskOrderDetail> getLocal(String methodName) {
    HashMap<String, List<CarRiskOrderDetail>> localMap = localCache.get();
    return localMap.get(methodName);
}

public void setLocal(String methodName, List<CarRiskOrderDetail> orderList) {
    HashMap<String, List<CarRiskOrderDetail>> localMap = localCache.get();
    localMap.put(methodName, orderList);
}

public Object getObjectLocal(String methodName) {
    HashMap<String, Object> localMap = localObjectCache.get();
    return localMap.get(methodName);
}

public void setObjectLocal(String methodName, Object orderList) {
    HashMap<String, Object> localMap = localObjectCache.get();
    localMap.put(methodName, orderList);
}
```

### 8.2 缓存清理

```java
try {
    // 策略检查逻辑...
    return strategyContext.getResult();
} finally {
    // 清理本地缓存，避免内存泄漏
    localCache.remove();
    localObjectCache.remove();
}
```

## 9. 策略模型详细说明

### 9.1 RiskStrategyDetail模型

```java
@Data
public class RiskStrategyDetail {
    private Long strategyId;           // 策略ID
    private String strategyNo;         // 策略编号
    private String name;               // 策略名称
    private List<String> productLine;  // 支持的业务线列表
    private List<String> channels;     // 支持的渠道列表
    private List<String> supplierCodes; // 支持的供应商编码列表
    private String scene;              // 场景编码
    private String script;             // 策略脚本
    private Integer level;             // 风险等级
    private Integer status;            // 状态：0-测试，1-生效，2-停用
    private String strategyWord;       // 策略提示语
    private String hitField;           // 命中字段
    private Integer hitAction;         // 命中动作：0-全局拉黑，1-1v1拉黑
    private Integer controlTime;       // 管控时间（天）
    private Integer controlType;       // 管控对象：0-司机，1-用户
    private Integer disposeAction;     // 处置动作：0-禁止，1-增强校验，2-通过
    private List<RiskRuleDetail> rules; // 规则列表
}
```

### 9.2 RiskRuleDetail模型

```java
@Data
public class RiskRuleDetail {
    private Long ruleId;               // 规则ID
    private String ruleNo;             // 规则编号
    private String name;               // 规则名称
    private String description;        // 规则描述
    private String script;             // 规则脚本
    private String expression;         // 规则表达式
    private List<RiskFieldDetail> riskFields; // 指标列表
}
```

### 9.3 RiskFieldDetail模型

```java
@Data
public class RiskFieldDetail {
    private String fieldId;            // 指标ID
    private String fieldNo;            // 指标编号
    private String script;             // 指标脚本
    private String operator;           // 操作符
    private Integer rightType;         // 右值类型
    private String rightValue;         // 右值
    private Integer sort;              // 排序
    private Integer category;          // 分类：0-全部，1-数值，2-布尔
    private Integer type;              // 类型：0-全部，1-风控，2-安全
    private Integer target;            // 目标：1-用户，2-司机，3-供应商
    private Integer basedCurrent;      // 是否基于当前订单：1-是，0-否
}
```

## 10. 脚本示例

### 10.1 策略脚本示例

```groovy
// 策略脚本：司机接单频率检查
def check(data) {
    // 检查规则1：司机当日接单次数
    def rule1 = data.get("rule1001")

    // 检查规则2：司机当日取消次数
    def rule2 = data.get("rule1002")

    // 策略逻辑：接单次数超过50次且取消次数超过10次
    return rule1 && rule2
}
```

### 10.2 规则脚本示例

```groovy
// 规则脚本：司机当日接单次数检查
def check(data) {
    // 获取指标值：司机当日接单次数
    def acceptCount = data.get("field2001")

    // 规则逻辑：接单次数大于50
    return acceptCount > 50
}
```

### 10.3 指标脚本示例

```groovy
// 指标脚本：计算司机当日接单次数
def check(data) {
    def carNum = data.get("carNum")
    def today = new Date().format("yyyy-MM-dd")

    // 调用数据查询服务
    def count = riskDataService.getDriverAcceptCountByDate(carNum, today)

    return ["num": count]
}
```

通过以上详细分析，我们可以看到策略检查模块是整个风控系统的核心，它通过灵活的策略-规则-指标三层架构，结合Groovy脚本的动态执行能力，实现了高度可配置和可扩展的风控检查逻辑。
