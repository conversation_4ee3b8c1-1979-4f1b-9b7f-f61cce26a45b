# car-risk项目 `/riskCheck/unifyCheck` 接口详细分析 - 第6部分：策略后处理和结果封装

## 1. 策略后处理总体流程

### 1.1 策略后处理在整体流程中的位置

策略后处理是策略检查的最后一个环节，负责对策略执行结果进行分析、判定和后续动作：

```java
public RiskSceneResult strategyCheck(UnifyCheckRequest request) {
    // 前置处理...
    
    try {
        // 1-5. 策略执行...
        
        // 6. 针对策略的后置处理
        strategyPostProcessing(strategyContext);
        
        return strategyContext.getResult();
    } finally {
        localCache.remove();
        localObjectCache.remove();
    }
}
```

### 1.2 策略后处理核心逻辑

```java
private void strategyPostProcessing(StrategyContext strategyContext) {
    // 1. 初始化响应结果
    RiskSceneResult result = new RiskSceneResult();
    strategyContext.setResult(result);
    
    Map<String, Object> params = strategyContext.getParams();
    // 释放订单参数，避免内存占用
    params.remove(ORDER);
    
    Map<String, RiskStrategyResult> strategyResultMap = strategyContext.getStrategyResultMap();
    
    // 2. 无策略匹配的情况
    if (CollUtil.isEmpty(strategyResultMap)) {
        result.setRiskFlag(false);
        result.setRiskMsg("风控策略通过");
        return;
    }
    
    // 3. 获取命中的策略
    List<Long> matchStrategyIds = strategyResultMap.values().stream()
            .filter(p -> p.getStrategyMatched())
            .map(p -> p.getStrategyId())
            .collect(Collectors.toList());
    
    List<RiskStrategyDetail> strategyList = strategyContext.getStrategyList().stream()
            .filter(p -> matchStrategyIds.contains(p.getStrategyId()))
            .collect(Collectors.toList());
    
    // 4. 过滤测试策略
    strategyList = strategyList.stream()
            .filter(p -> p.getStatus() != 0)
            .collect(Collectors.toList());
    
    if (CollUtil.isEmpty(strategyList)) {
        result.setRiskFlag(false);
        result.setRiskMsg("命中测试策略，风控策略通过");
        return;
    }
    
    // 5. 选择最高优先级策略
    RiskStrategyDetail disposeStrategy = strategyList.stream()
            .sorted(Comparator.comparing(RiskStrategyDetail::getLevel).reversed())
            .findFirst().get();
    
    List<String> matchedStrategyList = strategyList.stream()
            .map(p -> p.getStrategyNo())
            .collect(Collectors.toList());
    result.setMatchedStrategy(matchedStrategyList);
    
    // 6. 记录命中信息
    recordHitInfo(strategyContext, disposeStrategy, strategyResultMap);
    
    // 7. 过滤通过策略
    strategyList = strategyList.stream()
            .filter(p -> p.getDisposeAction() != 2)
            .collect(Collectors.toList());
    
    if (CollUtil.isEmpty(strategyList)) {
        result.setRiskFlag(false);
        result.setRiskMsg("命中策略无需拦截");
        return;
    }
    
    // 8. 设置拦截结果
    result.setRiskFlag(true);
    result.setRiskMsg(StringUtils.isBlank(disposeStrategy.getStrategyWord()) ? 
                     "风控拦截" : disposeStrategy.getStrategyWord());
    
    // 9. 执行策略动作
    for (RiskStrategyDetail strategy : strategyList) {
        doStrategyAction(params, strategy);
    }
}
```

## 2. 命中信息记录

### 2.1 记录命中信息

```java
private void recordHitInfo(StrategyContext strategyContext, RiskStrategyDetail disposeStrategy, 
                          Map<String, RiskStrategyResult> strategyResultMap) {
    RiskStrategyResult disposeStrategyResult = strategyResultMap.get(disposeStrategy.getStrategyNo());
    UnifyCheckRequest request = strategyContext.getRequest();
    
    if (null != request && null != disposeStrategyResult) {
        Integer controlType = disposeStrategy.getControlType();
        String hitField = disposeStrategy.getHitField();
        Integer disposeAction = disposeStrategy.getDisposeAction();
        
        // 设置命中信息到请求对象中
        request.setHitType(0);  // 0-策略命中
        request.setHitRule(String.join(",", disposeStrategyResult.getMatchRules()));
        request.setStrategyNos(disposeStrategyResult.getStrategyNo());
        request.setHitField(StringUtils.defaultString(hitField));
        request.setControlTarget(null == controlType ? "" : String.valueOf(controlType));
        request.setDisposeAction(null == disposeAction ? "" : String.valueOf(disposeAction));
        
        // 异步记录命中信息到数据库
        riskHitService.initHitRisk(request, strategyContext.getResult());
    }
}
```

### 2.2 RiskHitService命中记录

**文件位置**: `car-risk-process/src/main/java/com/ly/car/risk/process/service/RiskHitService.java`

```java
public void initHitRisk(UnifyCheckRequest request, RiskSceneResult result) {
    executorService.execute(() -> {
        RiskHit riskHit = new RiskHit();
        
        // 基础信息
        riskHit.setOrderId(StringUtils.defaultString(request.getOrderId()));
        if (StringUtils.isNotBlank(request.getScene()) && request.getScene().contains("-")) {
            String[] split = request.getScene().split("-");
            riskHit.setMainScene(Integer.parseInt(split[0]));
            riskHit.setChildScene(Integer.parseInt(split[1]));
        }
        
        // 用户信息
        riskHit.setMemberId(request.getMemberId());
        riskHit.setDriverCardNo(StringUtils.defaultString(request.getCarNum()));
        riskHit.setUnionId(StringUtils.defaultString(request.getUnionId()));
        riskHit.setUserPhone(StringUtils.defaultString(request.getPassengerPhone(),
                                                      StringUtils.defaultString(request.getUserPhone())));
        
        // 命中信息
        riskHit.setHitType(request.getHitType());
        riskHit.setHitRule(StringUtils.defaultString(request.getHitRule()));
        riskHit.setCustomerValue(StringUtils.defaultString(request.getCustomerValue()));
        riskHit.setCustomerType(request.getCustomerType() == null ? 0 : request.getCustomerType());
        riskHit.setHitStrategy(StringUtils.defaultString(request.getStrategyNos()));
        riskHit.setHitField(StringUtils.defaultString(request.getHitField()));
        riskHit.setControlTarget(StringUtils.defaultString(request.getControlTarget()));
        riskHit.setDisposeAction(StringUtils.defaultString(request.getDisposeAction()));
        
        // 请求和响应信息
        riskHit.setReqParam(JSON.toJSONString(request));
        riskHit.setResResult(JSON.toJSONString(result));
        riskHit.setRequestId(StringUtils.defaultString(request.getTraceId()));
        riskHit.setProductLine(StringUtils.defaultString(request.getProductLine()));
        
        // 场景信息
        Pair<MetricScene, MetricScene> pair = metricSceneService.getByNo(
                String.valueOf(riskHit.getMainScene()), 
                String.valueOf(riskHit.getChildScene()));
        if (null != pair && null != pair.getKey() && null != pair.getValue()) {
            riskHit.setMainSceneName(pair.getKey().getName());
            riskHit.setChildSceneName(pair.getValue().getName());
        }
        
        // 时间信息
        riskHit.setCreateTime(new Date());
        riskHit.setUpdateTime(new Date());
        riskHit.setRiskLevel(5);
        riskHit.setIsCheating(0);
        
        // 订单详情补充
        if (StringUtils.isNotBlank(request.getOrderId())) {
            fillOrderDetails(riskHit, request.getOrderId());
        }
        
        // 保存到数据库
        this.riskHitMapper.insert(riskHit);
    });
}
```

## 3. 策略动作执行

### 3.1 策略动作执行逻辑

```java
private void doStrategyAction(Map<String, Object> params, RiskStrategyDetail disposeStrategy) {
    // 检查必要参数
    if (StringUtils.isBlank(disposeStrategy.getHitField()) || 
        null == disposeStrategy.getControlTime() || 
        disposeStrategy.getControlTime() == 0) {
        return;
    }
    
    Integer controlTime = disposeStrategy.getControlTime();
    Integer riskType = disposeStrategy.getHitAction();
    String hitField = disposeStrategy.getHitField();
    String customerValue = null;
    Integer customerType = 0;
    
    // 获取基础参数
    String passengerPhone = (String) params.getOrDefault(PASSENGER_PHONE, StringUtils.EMPTY);
    String orderId = (String) params.getOrDefault(ORDER_ID, StringUtils.EMPTY);
    String productLine = (String) params.getOrDefault(PRODUCT_LINE, StringUtils.EMPTY);
    String supplierName = (String) params.getOrDefault(SUPPLIER_NAME, StringUtils.EMPTY);
    
    // 根据命中字段确定客户值和类型
    if (hitField.equals("driverCardNo")) {
        customerValue = (String) params.getOrDefault(CAR_NUM, StringUtils.EMPTY);
        customerType = RiskCustomerCustomerTypeEnum.car_number.getCode();
    } else if (hitField.equals("userId")) {
        customerValue = (String) params.getOrDefault(MEMBER_ID, StringUtils.EMPTY);
        customerType = RiskCustomerCustomerTypeEnum.user_id.getCode();
    } else if (hitField.equals("phone")) {
        customerValue = (String) params.getOrDefault(PASSENGER_PHONE, StringUtils.EMPTY);
        customerType = RiskCustomerCustomerTypeEnum.user_phone.getCode();
    }
    
    // 处理拉黑类型
    Integer resultDisposeType = 7; // 默认是1v1
    if (riskType == 0) {
        resultDisposeType = 1; // 如果命中动作是0全局拉黑的情况
    }
    
    // 萌艇不落名单
    if (!Objects.equals(StringUtils.upperCase(productLine), "MT")) {
        disposeCenterService.actionCustomer(customerValue, passengerPhone, controlTime,
                orderId, disposeStrategy.getStrategyNo(), resultDisposeType, customerType, supplierName);
    }
}
```

### 3.2 DisposeCenterService处置中心

```java
@Service
public class DisposeCenterService {
    
    @Resource
    private RiskCustomerService riskCustomerService;
    
    public void actionCustomer(String customerValue, String passengerPhone, Integer controlTime,
                              String orderId, String strategyNo, Integer disposeType, 
                              Integer customerType, String supplierName) {
        
        if (StringUtils.isBlank(customerValue)) {
            return;
        }
        
        try {
            if (disposeType == 1) {
                // 全局拉黑
                riskCustomerService.addRiskCustomer(customerValue, 
                        RiskCustomerRiskTypeEnum.black_list.getCode(), 
                        customerType, controlTime);
            } else if (disposeType == 7) {
                // 一对一拉黑
                riskCustomerService.addRiskCustomerOne(customerValue, 
                        RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode(),
                        customerType, controlTime, passengerPhone, orderId);
            }
            
            LoggerUtils.info(log, "策略动作执行成功，策略编号：{}，客户值：{}，处置类型：{}", 
                           strategyNo, customerValue, disposeType);
        } catch (Exception e) {
            LoggerUtils.error(log, "策略动作执行失败", e);
        }
    }
}
```

## 4. 结果封装

### 4.1 RiskSceneResult模型

```java
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RiskSceneResult {
    
    private boolean riskFlag;                    // 是否风险
    private List<String> matchedStrategy = new ArrayList<>();  // 命中的策略列表
    private String riskMsg;                      // 风险提示信息
    private Integer customerType;                // 客户类型
    
    // 静态工厂方法
    public static RiskSceneResult pass(String msg){
        RiskSceneResult result = new RiskSceneResult();
        result.setRiskFlag(false);
        result.setRiskMsg(msg);
        return result;
    }

    public static RiskSceneResult fail(String msg){
        RiskSceneResult result = new RiskSceneResult();
        result.setRiskFlag(true);
        result.setRiskMsg(msg);
        return result;
    }
    
    public static RiskSceneResult fail(String msg, Integer customerType){
        RiskSceneResult result = new RiskSceneResult();
        result.setRiskFlag(true);
        result.setRiskMsg(msg);
        result.setCustomerType(customerType);
        return result;
    }
}
```

### 4.2 UiResultWrapper统一响应封装

```java
@Data
public class UiResultWrapper<T> {
    private int code;        // 响应码
    private String message;  // 响应消息
    private T data;          // 响应数据
    
    public static <T> UiResultWrapper<T> ok(T data) {
        UiResultWrapper<T> wrapper = new UiResultWrapper<>();
        wrapper.setCode(0);
        wrapper.setMessage("success");
        wrapper.setData(data);
        return wrapper;
    }
    
    public static UiResultWrapper<Void> fail(int code, String message) {
        UiResultWrapper<Void> wrapper = new UiResultWrapper<>();
        wrapper.setCode(code);
        wrapper.setMessage(message);
        return wrapper;
    }
}
```

## 5. 策略检查后处理

### 5.1 afterStrategyCheck逻辑

```java
private void afterStrategyCheck(UnifyCheckRequest request, RiskSceneResult riskSceneResult) {
    // MT 且 司机认证场景的特殊处理
    if (Objects.equals(request.getProductLine(), ProductLineEnum.MT.getCode())
            && Objects.equals(request.getScene(), StrategySceneEnum.DRIVER_AUTHENTICATION.getScene())) {
        
        // 非风险则通知MT 司机认证成功
        if (null != riskSceneResult && !riskSceneResult.isRiskFlag()) {
            RiskResultNewDTO dto = new RiskResultNewDTO();
            Map<String, Object> data = new HashMap<>();
            data.put("driverId", StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.DRIVER_ID));
            data.put("idCard", StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.CERT_NO));
            dto.setObj(data);
            
            // 发送MQ通知
            mqSendConvertService.sendNotifyMq("MT", JsonUtils.json(dto));
        }
    }
}
```

### 5.2 beforeStrategyCheck逻辑

```java
private void beforeStrategyCheck(UnifyCheckRequest request) {
    // 如果是司机接单场景，并且是权益单，则将场景变更为权益活动
    if (Objects.equals(request.getScene(), DRIVER_ACCEPT_ORDER.getScene()) && request.isRightsOrderFlag()) {
        request.setScene(StrategySceneEnum.RIGHTS_ORDER.getScene());
    }
}
```

## 6. 策略后处理流程图

```mermaid
graph TD
    A[开始策略后处理] --> B[初始化结果对象]
    B --> C{是否有策略执行结果?}
    C -->|否| D[设置通过结果]
    C -->|是| E[获取命中的策略]
    E --> F[过滤测试策略]
    F --> G{过滤后是否还有策略?}
    G -->|否| H[设置测试策略通过结果]
    G -->|是| I[选择最高优先级策略]
    I --> J[记录命中信息]
    J --> K[过滤通过策略]
    K --> L{过滤后是否还有策略?}
    L -->|否| M[设置无需拦截结果]
    L -->|是| N[设置拦截结果]
    N --> O[执行策略动作]
    O --> P[策略检查后处理]
    
    subgraph "命中信息记录"
        Q[设置命中类型为策略]
        R[设置命中规则]
        S[设置策略编号]
        T[设置命中字段]
        U[异步保存到数据库]
    end
    
    subgraph "策略动作执行"
        V[确定客户值和类型]
        W[确定拉黑类型]
        X[调用处置中心]
        Y[添加风控名单]
    end
    
    subgraph "特殊场景后处理"
        Z[MT司机认证成功通知]
        AA[发送MQ消息]
    end
    
    J --> Q
    O --> V
    P --> Z
    
    D --> BB[返回结果]
    H --> BB
    M --> BB
    P --> BB
```

## 7. 响应示例

### 7.1 通过响应示例

```json
{
    "code": 0,
    "message": "success",
    "data": {
        "riskFlag": false,
        "matchedStrategy": [],
        "riskMsg": "风控策略通过",
        "customerType": null
    }
}
```

### 7.2 拦截响应示例

```json
{
    "code": 0,
    "message": "success", 
    "data": {
        "riskFlag": true,
        "matchedStrategy": ["STRATEGY_001", "STRATEGY_002"],
        "riskMsg": "司机接单频率异常，暂时禁止接单",
        "customerType": 6
    }
}
```

### 7.3 异常响应示例

```json
{
    "code": -1,
    "message": "系统异常",
    "data": null
}
```

通过以上详细分析，我们可以看到策略后处理模块不仅负责结果的判定和封装，还承担了命中信息记录、策略动作执行、特殊场景后处理等重要职责，确保了整个风控流程的完整性和有效性。
