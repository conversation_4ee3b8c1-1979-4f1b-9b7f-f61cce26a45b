# car-risk项目 `/riskCheck/unifyCheck` 接口详细分析 - 第3部分：黑名单检查处理

## 1. 黑名单检查总体架构

### 1.1 黑名单检查在整体流程中的位置

黑名单检查是unifyCheck接口的第一道防线，在参数校验通过后立即执行：

```java
public RiskSceneResult unifyCheck(UnifyCheckRequest request) throws BizException {
    // 1. 参数校验
    paramCheck(request);

    // 2. 黑名单检查 - 第一道防线
    RiskSceneResult blackListCheckResult = blackListHandler.blackListCheck(request);
    if(null != blackListCheckResult){
        return blackListCheckResult;  // 命中黑名单直接返回
    }

    // 3. 分销单判断
    if (request.isDistributionFlag()) {
        return RiskSceneResult.pass("分销单判定风险名单通过");
    }

    // 4. 后续处理...
}
```

### 1.2 黑名单处理器架构

黑名单检查采用策略模式，根据不同业务线选择不同的处理器：

```
BlackListHandler (统一入口)
├── MTBlackListHandler (萌艇业务线专用)
└── CommonBlackListHandler (通用业务线)
```

## 2. 黑名单处理器详细分析

### 2.1 BlackListHandler - 统一入口

**文件位置**: `car-risk-process/src/main/java/com/ly/car/risk/process/strategy/blacklist/BlackListHandler.java`

```java
@Service
public class BlackListHandler implements BlackListCheck {

    @Resource
    private MTBlackListHandler mtBlackListHandler;

    @Resource
    private CommonBlackListHandler commonBlackListHandler;

    @Override
    public RiskSceneResult blackListCheck(UnifyCheckRequest request) {
        // 预处理：分销单特殊处理
        beforeBlackWhiteCheck(request);
        
        StrategySceneEnum scene = StrategySceneEnum.of(request.getScene());

        // 根据业务线选择不同的处理器
        if (Objects.equals(request.getProductLine(), ProductLineEnum.MT.getCode())) {
            // MT业务线使用专用处理器
            return mtBlackListHandler.blackListCheck(request);
        } else if (scene == StrategySceneEnum.CANCEL_REMINDER || scene == StrategySceneEnum.FINISH_ORDER) {
            // 取消提醒和订单完成场景不校验黑名单
            return null;
        } else {
            // 其他业务线使用通用处理器
            return commonBlackListHandler.blackListCheck(request);
        }
    }
    
    private void beforeBlackWhiteCheck(UnifyCheckRequest request) {
        if (request.isDistributionFlag()) {
            // 分销单的memberId是批量虚拟账号，不校验memberId相关，防止误伤
            request.setMemberId(StringUtils.EMPTY);
        }
    }
}
```

### 2.2 CommonBlackListHandler - 通用黑名单处理器

**文件位置**: `car-risk-process/src/main/java/com/ly/car/risk/process/strategy/blacklist/CommonBlackListHandler.java`

#### 2.2.1 核心处理逻辑

```java
@Override
public RiskSceneResult blackListCheck(UnifyCheckRequest request) {
    RiskCustomerRiskTypeEnum blackWhiteCheckResult = blackWhiteCheck(request);
    
    // 白名单直接通过
    if (Objects.equals(blackWhiteCheckResult, RiskCustomerRiskTypeEnum.white_list)) {
        return RiskSceneResult.pass(blackWhiteCheckResult.getTip().getMsg());
    } 
    // 命中其他风险类型（黑名单等）
    else if (null != blackWhiteCheckResult) {
        RiskSceneResult fail = RiskSceneResult.fail(blackWhiteCheckResult.getTip().getMsg(), 
                                                   blackWhiteCheckResult.getCode());
        // 记录命中信息
        riskHitService.initHitRisk(request, fail);
        return fail;
    }
    
    return null; // 未命中任何名单，继续后续检查
}
```

#### 2.2.2 黑白名单检查详细流程

```java
private RiskCustomerRiskTypeEnum blackWhiteCheck(UnifyCheckRequest request) {
    
    // 1. 获取所有相关的客户名单记录
    List<RiskCustomerManage> allCustomerList = getAllCustomerList(request);
    
    // 2. 根据场景过滤名单（主要针对司机接单场景）
    List<RiskCustomerManage> filterCustomerList = filterByScene(request, allCustomerList);
    
    // 3. 特殊黑名单判断（腾讯出行专用逻辑）
    RiskCustomerRiskTypeEnum hitCustomerRiskType = specialBlackJudge(request, filterCustomerList, allCustomerList);
    if (hitCustomerRiskType != null) {
        return hitCustomerRiskType;
    }

    // 4. 一对一名单检查（司机接单场景）
    if (StringUtils.isNotBlank(request.getCarNum()) && StringUtils.isNotBlank(request.getPassengerPhone())) {
        Optional<RiskCustomerManage> optional = allCustomerList.stream().filter(p ->
                Objects.equals(p.getRiskType(), RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode())
                && Objects.equals(p.getCustomerValue(), request.getCarNum())
                && Objects.equals(p.getBindUser(), request.getPassengerPhone())).findFirst();
        if (optional.isPresent()) {
            RiskCustomerManage riskCustomerManage = optional.get();
            // 记录命中信息
            request.setHitType(1);
            request.setHitRule(RiskCustomerRiskTypeEnum.getMsgByCode(riskCustomerManage.getRiskType()));
            request.setCustomerValue(riskCustomerManage.getCustomerValue());
            request.setCustomerType(riskCustomerManage.getCustomerType());
            return RiskCustomerRiskTypeEnum.ban_one_to_one_list;
        }
    }

    // 5. 白名单检查
    if (filterCustomerList.stream().anyMatch(p -> Objects.equals(RiskCustomerRiskTypeEnum.white_list.getCode(), p.getRiskType()))) {
        return RiskCustomerRiskTypeEnum.white_list;
    }

    // 6. 黑名单检查
    List<Integer> riskTypes = StrategySceneEnum.sceneRiskType(request.getScene(), request.getProductLine());
    List<RiskCustomerManage> matchBlackList = filterCustomerList.stream()
            .filter(p -> riskTypes.contains(p.getRiskType()))
            .sorted(Comparator.comparing(RiskCustomerManage::getRiskType))
            .collect(Collectors.toList());

    if (CollUtil.isEmpty(matchBlackList)) {
        return null;
    }

    RiskCustomerManage riskCustomerManage = matchBlackList.get(0);

    // 记录命中信息
    request.setHitType(1);
    request.setHitRule(RiskCustomerRiskTypeEnum.getMsgByCode(riskCustomerManage.getRiskType()));
    request.setCustomerValue(riskCustomerManage.getCustomerValue());
    request.setCustomerType(riskCustomerManage.getCustomerType());

    return RiskCustomerRiskTypeEnum.of(riskCustomerManage.getRiskType());
}
```

## 3. 数据查询和过滤逻辑

### 3.1 获取客户名单数据

```java
private List<RiskCustomerManage> getAllCustomerList(UnifyCheckRequest request) {
    CommonCustomerParam customerParam = new CommonCustomerParam();
    customerParam.setUserPhone(request.getUserPhone());
    customerParam.setPassengerCellphone(request.getPassengerPhone());
    customerParam.setMemberId(request.getMemberId());
    customerParam.setDriverCardNo(request.getCarNum());
    customerParam.setUnionId(request.getUnionId());
    customerParam.setIdCardNos(request.getCardNos());
    customerParam.setPayAccount(request.getPayAccount());
    customerParam.setDeviceId(request.getDeviceId());
    
    // 查询有效的风控名单记录
    return this.riskCustomerService.getListByValueByGroup(customerParam, new Date());
}
```

### 3.2 场景过滤逻辑

```java
private List<RiskCustomerManage> filterByScene(UnifyCheckRequest request, List<RiskCustomerManage> customerManageList) {
    
    if(CollectionUtils.isEmpty(customerManageList)){
        return customerManageList;
    }
    
    StrategySceneEnum sceneEnum = StrategySceneEnum.of(request.getScene());
    if (null == sceneEnum) {
        return customerManageList;
    }
    
    Predicate<RiskCustomerManage> predicate;
    
    switch (sceneEnum) {
        case DRIVER_ACCEPT_ORDER:
        case DISPATCHER_ACCEPT_ORDER:
            // 司机接单场景：只检查车牌号和设备ID相关的名单
            predicate = customer -> Objects.equals(customer.getCustomerValue(), request.getCarNum())
                    || Objects.equals(customer.getCustomerValue(), request.getDeviceId());
            break;
        default:
            // 其他场景：检查所有相关名单
            return customerManageList;
    }
    
    return customerManageList.parallelStream().filter(predicate).collect(Collectors.toList());
}
```

### 3.3 数据库查询SQL分析

**Mapper文件**: `car-risk-process/src/main/java/com/ly/car/risk/process/repo/risk/mapper/RiskCustomerManageMapper.xml`

```xml
<select id="getListByValueByGroup" resultType="com.ly.car.risk.entity.RiskCustomerManage">
    SELECT * FROM risk_customer_manage 
    WHERE status = 1 
    AND invalid_time > #{invalidTime}
    AND (
        <if test="params.userPhone != null and params.userPhone != ''">
            (customer_type = 2 AND customer_value = #{params.userPhone}) OR
        </if>
        <if test="params.passengerCellphone != null and params.passengerCellphone != ''">
            (customer_type = 2 AND customer_value = #{params.passengerCellphone}) OR
        </if>
        <if test="params.memberId != null and params.memberId != ''">
            (customer_type = 1 AND customer_value = #{params.memberId}) OR
        </if>
        <if test="params.driverCardNo != null and params.driverCardNo != ''">
            (customer_type = 6 AND customer_value = #{params.driverCardNo}) OR
        </if>
        <if test="params.unionId != null and params.unionId != ''">
            (customer_type = 3 AND customer_value = #{params.unionId}) OR
        </if>
        <if test="params.deviceId != null and params.deviceId != ''">
            (customer_type = 4 AND customer_value = #{params.deviceId}) OR
        </if>
        <if test="params.payAccount != null and params.payAccount != ''">
            (customer_type = 5 AND customer_value = #{params.payAccount}) OR
        </if>
        <if test="params.idCardNos != null and params.idCardNos.size() > 0">
            <foreach collection="params.idCardNos" item="idCard" separator=" OR ">
                (customer_type = 10 AND customer_value = #{idCard})
            </foreach>
        </if>
        1=0
    )
    ORDER BY risk_type ASC, create_time DESC
</select>
```

## 4. 特殊场景处理

### 4.1 腾讯出行特殊逻辑

```java
private static RiskCustomerRiskTypeEnum specialBlackJudge(UnifyCheckRequest request, 
                                                         List<RiskCustomerManage> filterCustomerList, 
                                                         List<RiskCustomerManage> allCustomerList) {

    // 腾讯出行额外校验，场景5-1、5-2
    // 腾讯一对一名单->腾讯黑名单->现有名单过滤顺序->风控策略
    if (Objects.equals(request.getChannel(), MetricStrategyChannelEnum.TX_TRAVEL.code)
            && (Objects.equals(request.getScene(), DRIVER_ACCEPT_ORDER.getScene()) 
                || Objects.equals(request.getScene(), DISPATCHER_ACCEPT_ORDER.getScene()))) {
        
        // 1. 腾讯一对一名单检查
        Optional<RiskCustomerManage> hit1v1Optional = allCustomerList.stream().filter(p ->
                Objects.equals(RiskCustomerRiskTypeEnum.tx_ban_one_to_one_list.getCode(), p.getRiskType())
                        && (Objects.equals(request.getUserPhone(), p.getBindUser()) 
                            || Objects.equals(request.getPassengerPhone(), p.getBindUser()))
                        && Objects.equals(request.getCarNum(), p.getCustomerValue())
        ).findFirst();

        if (hit1v1Optional.isPresent()) {
            RiskCustomerManage riskCustomerManage = hit1v1Optional.get();
            // 记录命中信息
            request.setHitType(1);
            request.setHitRule(RiskCustomerRiskTypeEnum.getMsgByCode(riskCustomerManage.getRiskType()));
            request.setCustomerValue(riskCustomerManage.getCustomerValue());
            request.setCustomerType(riskCustomerManage.getCustomerType());
            return RiskCustomerRiskTypeEnum.tx_ban_one_to_one_list;
        }

        // 2. 腾讯黑名单检查
        Optional<RiskCustomerManage> hitBlackOptional = filterCustomerList.stream().filter(p ->
                Objects.equals(RiskCustomerRiskTypeEnum.tx_black_list.getCode(), p.getRiskType()) 
                && Objects.equals(request.getCarNum(), p.getCustomerValue())
        ).findFirst();
        
        if (hitBlackOptional.isPresent()) {
            RiskCustomerManage riskCustomerManage = hitBlackOptional.get();
            // 记录命中信息
            request.setHitType(1);
            request.setHitRule(RiskCustomerRiskTypeEnum.getMsgByCode(riskCustomerManage.getRiskType()));
            request.setCustomerValue(riskCustomerManage.getCustomerValue());
            request.setCustomerType(riskCustomerManage.getCustomerType());
            return RiskCustomerRiskTypeEnum.tx_black_list;
        }
    }
    return null;
}
```

### 4.2 分销单特殊处理

```java
private void beforeBlackWhiteCheck(UnifyCheckRequest request) {
    if (request.isDistributionFlag()) {
        // 分销单的memberId是批量虚拟账号，不校验memberId相关，防止误伤
        request.setMemberId(StringUtils.EMPTY);
    }
}
```

## 5. 风险类型枚举详细说明

### 5.1 RiskCustomerRiskTypeEnum定义

```java
public enum RiskCustomerRiskTypeEnum {
    black_list(1, "黑名单"),
    white_list(2, "白名单"),
    ban_coupon_list(3, "禁止领券名单"),
    ban_reward_list(4, "禁止奖励名单"),
    ban_dispatch_list(5, "禁止派单名单"),
    ban_accept_list(6, "禁止接单名单"),
    ban_one_to_one_list(7, "一对一名单"),
    ban_register_list(8, "禁止注册名单"),
    tx_black_list(9, "腾讯黑名单"),
    tx_ban_one_to_one_list(10, "腾讯一对一名单");
    
    private final Integer code;
    private final RiskCustomerRiskTipTypeEnum tip;
}
```

### 5.2 客户类型枚举定义

```java
public enum RiskCustomerCustomerTypeEnum {
    user_id(1, "用户ID"),
    user_phone(2, "用户手机号"),
    union_id(3, "微信unionId"),
    device_id(4, "设备ID"),
    pay_account(5, "支付账号"),
    car_number(6, "司机车牌号"),
    hc_member_id(7, "汇川用户ID"),
    driver_id(8, "司机ID"),
    invite_phone(9, "邀请人手机号"),
    id_card_no(10, "身份证号");
    
    private final Integer code;
    private final String desc;
}
```

## 6. 黑名单检查流程图

```mermaid
graph TD
    A[开始黑名单检查] --> B[预处理：分销单memberId置空]
    B --> C{判断业务线}
    C -->|MT| D[使用MTBlackListHandler]
    C -->|其他| E{判断场景}
    E -->|取消提醒/订单完成| F[跳过黑名单检查]
    E -->|其他场景| G[使用CommonBlackListHandler]
    
    G --> H[获取所有相关客户名单]
    H --> I[根据场景过滤名单]
    I --> J[腾讯出行特殊检查]
    J --> K{命中腾讯名单?}
    K -->|是| L[返回腾讯名单结果]
    K -->|否| M[一对一名单检查]
    M --> N{命中一对一名单?}
    N -->|是| O[返回一对一名单结果]
    N -->|否| P[白名单检查]
    P --> Q{命中白名单?}
    Q -->|是| R[返回白名单通过]
    Q -->|否| S[黑名单检查]
    S --> T{命中黑名单?}
    T -->|是| U[记录命中信息并返回拦截结果]
    T -->|否| V[返回null继续后续检查]
    
    D --> W[MT专用逻辑处理]
    F --> V
    L --> X[记录命中信息]
    O --> X
    U --> X
    R --> Y[直接返回通过]
    X --> Z[结束]
    Y --> Z
    V --> Z
    W --> Z
```

通过以上详细分析，我们可以看到黑名单检查模块设计精巧，既考虑了不同业务线的差异化需求，又针对特殊场景（如腾讯出行、分销单）做了专门的处理逻辑，确保了风控检查的准确性和业务的灵活性。
