# car-risk项目 `/riskCheck/unifyCheck` 接口详细分析 - 第1部分：项目总览和架构

## 1. 项目总体架构

### 1.1 模块结构

car-risk项目采用多模块架构设计，主要包含以下模块：

```
car-risk/
├── car-risk-api/          # API接口定义模块
├── car-risk-common/       # 公共组件模块  
├── car-risk-impl/         # 实现模块
├── car-risk-manage/       # 管理后台模块
└── car-risk-process/      # 核心处理模块（unifyCheck所在模块）
```

### 1.2 car-risk-process模块架构

car-risk-process是核心风控处理模块，包含以下主要包结构：

```
com.ly.car.risk.process/
├── controller/           # 控制器层
│   ├── RiskCheckController.java    # 风控检查控制器（unifyCheck接口入口）
│   └── request/         # 请求参数类
│       └── UnifyCheckRequest.java  # 统一检查请求参数
├── strategy/            # 策略处理层
│   ├── RiskStrategyHandler.java    # 风控策略处理器（核心逻辑）
│   ├── RiskStrategyHelper.java     # 策略加载辅助类
│   ├── blacklist/       # 黑名单处理
│   │   ├── BlackListHandler.java   # 黑名单处理器
│   │   ├── CommonBlackListHandler.java  # 通用黑名单处理
│   │   └── MTBlackListHandler.java      # MT业务线黑名单处理
│   └── model/           # 策略模型类
├── scene/               # 特殊场景处理
│   ├── SpecialSceneHandlerFactory.java  # 特殊场景处理工厂
│   └── BankCardChangeSceneHandler.java  # 银行卡变更场景处理器
├── service/             # 业务服务层
│   ├── RiskCustomerService.java    # 风控客户服务
│   ├── RiskHitService.java         # 风控命中记录服务
│   └── core/            # 核心服务
├── repo/                # 数据访问层
│   └── risk/mapper/     # 风控相关Mapper
└── constants/           # 常量定义
```

## 2. unifyCheck接口总体设计

### 2.1 接口定义

**接口路径**: `/riskCheck/unifyCheck`  
**请求方式**: POST  
**功能描述**: 统一风控检查接口，支持多种业务场景的风控检查

### 2.2 核心设计理念

1. **统一入口**: 所有业务场景的风控检查都通过这一个接口
2. **分层处理**: 按照黑名单检查 → 特殊场景检查 → 策略检查的顺序执行
3. **可扩展性**: 支持新增业务场景、策略规则、黑名单类型
4. **高性能**: 采用缓存、异步处理等优化手段
5. **可配置**: 策略规则、场景配置等支持动态配置

### 2.3 支持的业务场景

根据代码分析，unifyCheck接口支持以下主要业务场景：

| 场景编码 | 场景名称 | 业务描述 |
|---------|---------|---------|
| 1-1 | DRIVER_REGISTER | 司机注册场景 |
| 2-1 | USER_CREATE_ORDER | 用户下单场景 |
| 2-2 | USER_DISPATCHING_ORDER | 用户派单场景 |
| 5-1 | DRIVER_ACCEPT_ORDER | 司机接单场景 |
| 5-2 | DISPATCHER_ACCEPT_ORDER | 调度员接单场景 |
| 6-1 | DRIVER_AUTHENTICATION | 司机认证场景 |
| 7-1 | BANK_CARD_CHANGE | 银行卡变更场景 |
| 8-1 | FINISH_ORDER | 订单完成场景 |
| 9-1 | ACT_COUPON_LQ | 活动领券场景 |
| 10-1 | CANCEL_REMINDER | 取消提醒场景 |
| 11-1 | RIGHTS_ORDER | 权益订单场景 |

### 2.4 支持的业务线

| 业务线编码 | 业务线名称 | 描述 |
|-----------|-----------|------|
| YNC | 网约车 | 网约车业务线 |
| SFC | 顺风车 | 顺风车业务线 |
| MT | 萌艇 | 萌艇业务线 |
| BUS | 汽车票 | 汽车票业务线 |
| LINE | 班线 | 班线业务线 |

## 3. 核心数据库表结构

### 3.1 风控名单相关表

#### 3.1.1 risk_customer_manage（风控客户管理表）

```sql
CREATE TABLE `risk_customer_manage` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `customer_type` int(11) NOT NULL COMMENT '客户类型：1-用户ID，2-用户手机号，6-司机车牌号，10-身份证号等',
  `customer_value` varchar(255) NOT NULL COMMENT '客户值（车牌号、手机号等）',
  `risk_type` int(11) NOT NULL COMMENT '风险类型：1-黑名单，2-白名单，7-一对一名单等',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态：1-有效，2-失效，3-已删除',
  `ttl` int(11) NOT NULL DEFAULT '-1' COMMENT '有效期限（天数），-1表示永久有效',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `invalid_time` datetime DEFAULT NULL COMMENT '失效时间',
  `option_type` int(11) DEFAULT '1' COMMENT '操作类型：1-系统操作，2-人工操作，3-客户操作',
  `create_user` varchar(255) DEFAULT NULL COMMENT '创建人',
  `option_name` varchar(255) DEFAULT NULL COMMENT '操作人',
  `risk_remark` text COMMENT '风险备注（拉黑原因）',
  `bind_user` varchar(255) DEFAULT NULL COMMENT '绑定用户（一对一名单中的乘客手机号）',
  `bind_order` varchar(255) DEFAULT NULL COMMENT '绑定订单',
  `supplier_name` varchar(255) DEFAULT NULL COMMENT '供应商名称',
  PRIMARY KEY (`id`),
  KEY `idx_customer_value` (`customer_value`),
  KEY `idx_customer_type_risk_type` (`customer_type`, `risk_type`),
  KEY `idx_status_invalid_time` (`status`, `invalid_time`)
) COMMENT='风控客户管理表';
```

#### 3.1.2 风险类型枚举说明

| risk_type | 名称 | 描述 |
|-----------|------|------|
| 1 | 黑名单 | 全局黑名单，禁止所有操作 |
| 2 | 白名单 | 白名单，跳过风控检查 |
| 3 | 禁止领券名单 | 禁止参与优惠券活动 |
| 4 | 禁止奖励名单 | 禁止获得奖励 |
| 5 | 禁止派单名单 | 禁止派单 |
| 6 | 禁止接单名单 | 禁止接单 |
| 7 | 一对一名单 | 特定司机与乘客的一对一禁止 |
| 8 | 禁止注册名单 | 禁止注册 |
| 9 | 腾讯黑名单 | 腾讯出行专用黑名单 |
| 10 | 腾讯一对一名单 | 腾讯出行专用一对一名单 |

### 3.2 策略规则相关表

#### 3.2.1 metric_strategy（风控策略表）

```sql
CREATE TABLE `metric_strategy` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `name` varchar(255) NOT NULL COMMENT '策略名称',
  `strategy_no` varchar(50) NOT NULL COMMENT '策略编号',
  `channels` varchar(255) DEFAULT NULL COMMENT '渠道多选',
  `strategy_type` int(11) DEFAULT NULL COMMENT '策略分类',
  `level` int(11) DEFAULT NULL COMMENT '风险等级：1-低，2-中，3-高',
  `risk_type` varchar(255) DEFAULT NULL COMMENT '风险类型',
  `product_lines` varchar(255) DEFAULT NULL COMMENT '业务线',
  `scene` varchar(50) DEFAULT NULL COMMENT '场景',
  `supplier_codes` varchar(500) DEFAULT NULL COMMENT '供应商编码',
  `script` text COMMENT '策略脚本',
  `control_type` int(11) DEFAULT NULL COMMENT '管控对象：0-司机，1-用户',
  `hit_field` varchar(100) DEFAULT NULL COMMENT '命中字段',
  `hit_action` int(11) DEFAULT NULL COMMENT '命中动作：0-加全局黑，1-加1v1',
  `control_time` int(11) DEFAULT NULL COMMENT '管控时间（天数），-1表示永久',
  `dispose_action` int(11) DEFAULT NULL COMMENT '处置动作：0-禁止，1-增强校验，2-通过',
  `status` int(11) DEFAULT '1' COMMENT '状态：0-测试，1-生效，2-停用',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_strategy_no` (`strategy_no`),
  KEY `idx_product_lines_scene` (`product_lines`, `scene`)
) COMMENT='风控策略表';
```

#### 3.2.2 metric_rule（风控规则表）

```sql
CREATE TABLE `metric_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `name` varchar(255) NOT NULL COMMENT '规则名称',
  `rule_no` varchar(50) NOT NULL COMMENT '规则编号',
  `description` text COMMENT '规则描述',
  `script` text COMMENT '规则脚本',
  `expression` text COMMENT '规则表达式',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rule_no` (`rule_no`)
) COMMENT='风控规则表';
```

#### 3.2.3 metric_field（风控指标表）

```sql
CREATE TABLE `metric_field` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `name` varchar(255) NOT NULL COMMENT '指标名称',
  `field_no` varchar(50) NOT NULL COMMENT '指标编号',
  `description` text COMMENT '描述',
  `script` text COMMENT '特征指标代码',
  `category` int(11) DEFAULT NULL COMMENT '0-全部，1-数值，2-布尔',
  `type` int(11) DEFAULT NULL COMMENT '0-全部，1-风控，2-安全',
  `target` int(11) DEFAULT NULL COMMENT '1-用户，2-司机，3-供应商',
  `based_current` int(11) DEFAULT '0' COMMENT '1-基于当前订单，0-基于历史订单',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_field_no` (`field_no`)
) COMMENT='风控指标表';
```

### 3.3 命中记录相关表

#### 3.3.1 risk_hit（风控命中记录表）

```sql
CREATE TABLE `risk_hit` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `order_id` varchar(100) DEFAULT NULL COMMENT '订单ID',
  `main_scene` int(11) DEFAULT NULL COMMENT '主场景',
  `child_scene` int(11) DEFAULT NULL COMMENT '子场景',
  `member_id` varchar(100) DEFAULT NULL COMMENT '用户ID',
  `req_param` text COMMENT '请求参数',
  `risk_level` int(11) DEFAULT NULL COMMENT '风险等级',
  `hit_type` int(11) DEFAULT NULL COMMENT '命中类型：0-策略，1-名单',
  `hit_rule` varchar(500) DEFAULT NULL COMMENT '命中规则',
  `customer_value` varchar(255) DEFAULT NULL COMMENT '客户值',
  `customer_type` int(11) DEFAULT NULL COMMENT '客户类型',
  `product_line` varchar(50) DEFAULT NULL COMMENT '业务线',
  `request_id` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `res_result` text COMMENT '响应结果',
  `driver_card_no` varchar(100) DEFAULT NULL COMMENT '司机车牌号',
  `union_id` varchar(100) DEFAULT NULL COMMENT '微信unionId',
  `hit_strategy` varchar(500) DEFAULT NULL COMMENT '命中策略',
  `hit_field` varchar(100) DEFAULT NULL COMMENT '命中字段',
  `control_target` varchar(50) DEFAULT NULL COMMENT '管控对象',
  `dispose_action` varchar(50) DEFAULT NULL COMMENT '处置动作',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_create_time` (`create_time`)
) COMMENT='风控命中记录表';
```

## 4. 核心处理流程概述

unifyCheck接口的核心处理流程如下：

```
1. 接收请求 (RiskCheckController.unifyCheck)
   ↓
2. 参数校验 (RiskStrategyHandler.paramCheck)
   ↓
3. 黑名单检查 (BlackListHandler.blackListCheck)
   ↓ (如果命中黑名单，直接返回拦截结果)
4. 分销单判断 (如果是分销单，直接通过)
   ↓
5. 特殊场景检查 (SpecialSceneHandlerFactory.handlerCheck)
   ↓ (如果命中特殊场景规则，返回相应结果)
6. 策略检查前处理 (beforeStrategyCheck)
   ↓
7. 策略检查 (strategyCheck)
   ├── 构建上下文 (buildContext)
   ├── 参数加密 (encryptParam)
   ├── 查找策略 (findStrategy)
   ├── 填充订单参数 (fillOrderParam)
   ├── 执行策略 (strategyHandle)
   └── 策略后处理 (strategyPostProcessing)
   ↓
8. 策略检查后处理 (afterStrategyCheck)
   ↓
9. 返回结果
```

这个流程设计确保了：
- **安全性**: 优先检查黑名单，确保高风险用户被及时拦截
- **性能**: 分层检查，避免不必要的复杂计算
- **灵活性**: 支持特殊场景的定制化处理
- **可扩展性**: 策略规则可以动态配置和扩展

在接下来的文档中，我们将详细分析每个处理环节的具体实现逻辑。
