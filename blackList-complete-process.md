# 黑名单功能完整流程分析

## 1. 黑名单功能概述

黑名单功能是风控系统的重要组成部分，用于管理和控制不同类型的风险用户和司机。主要包括以下功能：

1. **添加黑名单**：将用户或司机添加到黑名单中
2. **查询黑名单**：查询黑名单列表或检查特定用户/司机是否在黑名单中
3. **移除黑名单**：将用户或司机从黑名单中移除
4. **同步黑名单**：与外部系统同步黑名单数据

## 2. 黑名单类型

系统支持多种类型的黑名单，主要包括：

1. **全局黑名单**（risk_type=1）：被全局拉黑的司机，无法接任何订单
2. **一对一黑名单**（risk_type=7）：特定用户拉黑特定司机，该司机只对该用户不可见
3. **禁止接单名单**（risk_type=6）：司机被禁止接单
4. **禁止网约车接单名单**（risk_type=9）：司机被禁止接网约车订单
5. **腾讯黑名单**（risk_type=21）：来自腾讯出行的黑名单
6. **腾讯一对一名单**（risk_type=22）：来自腾讯出行的一对一黑名单

## 3. 黑名单数据流向图

```mermaid
graph TD
    A1[管理端] -->|新增、导入、修改、状态更新| B1[RiskCustomerController]
    B1 --> C1[riskCustomerService]

    A[用户端] -->|拉黑司机| B[BlackListApiController]
    B -->|blackDriver| C[BlackListService]
    
    D[客服端] -->|拉黑司机| E[BlackListController]
    E -->|blackDriverFromManage| C
    
    F[外部系统] -->|同步黑名单| G[BlackListApiController]
    G -->|syncDriverBlack| C
    
    H[系统定时任务] -->|自动拉黑| C

    C1 --> I[risk_customer_manage表]
    C1 --> J[risk_customer_record表]
    
    C -->|写入数据| I[risk_customer_manage表]
    C -->|记录操作| J[risk_customer_record表]
    
    K[用户端] -->|查询黑名单| L[BlackListApiController]
    L -->|listDriver| C
    C -->|读取数据| I
    
    M[用户端] -->|取消拉黑| N[BlackListApiController]
    N -->|removeDriver| C
    C -->|更新数据| I
```

## 4. `/blackList/driver/list` 接口完整流程

### 4.1 接口调用流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as BlackListApiController
    participant Service as BlackListService
    participant Mapper as RiskCustomerManageMapper
    participant DB as 数据库
    
    Client->>Controller: POST /blackList/driver/list
    Controller->>Controller: 记录请求日志
    Controller->>Service: listDriver(request)
    
    Service->>Service: 检查乘客手机号是否为空
    alt 手机号为空
        Service-->>Controller: 返回空结果
    else 手机号不为空
        Service->>Mapper: selectList(QueryWrapper)
        Mapper->>DB: 执行SQL查询
        DB-->>Mapper: 返回查询结果
        Mapper-->>Service: List<RiskCustomerManage>
        
        Service->>Service: 过滤无效数据
        Service->>Service: 转换为DTO对象
        Service-->>Controller: UiResult<List<RiskCustomerManageListDTO>>
    end
    
    Controller->>Controller: 记录响应日志
    Controller-->>Client: UiResultWrapper
```

### 4.2 数据库查询

```sql
SELECT * FROM risk_customer_manage
WHERE bind_user = #{passengerCellphone}
  AND invalid_time > NOW()
```

### 4.3 数据过滤与转换

```java
// 过滤无效数据
if (StringUtils.isBlank(manage.getBindOrder()) || StringUtils.isBlank(manage.getStartAddress())) {
    continue;
}

// 转换为DTO对象
RiskCustomerManageListDTO dto = new RiskCustomerManageListDTO();
dto.setInvalidTime(DateUtil.date2String(manage.getInvalidTime()));
dto.setDriverName(manage.getDriverName());
dto.setCustomerValue(manage.getCustomerValue());
dto.setOrderId(manage.getBindOrder());
dto.setShieldTime(DateUtil.date2String(manage.getUpdateTime()));
dto.setStartAddress(manage.getStartAddress());
dto.setEndAddress(manage.getEndAddress());
dto.setUseTime(manage.getUseTime());
dto.setDriverCardNo(manage.getCustomerValue());
dtoList.add(dto);
```

## 5. 黑名单添加流程

### 5.1 用户拉黑司机

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as BlackListApiController
    participant Service as BlackListService
    participant Mapper as RiskCustomerManageMapper
    participant DB as 数据库
    
    Client->>Controller: POST /blackList/driver/black
    Controller->>Service: blackDriver(request)
    
    Service->>Service: 参数校验
    Service->>Mapper: 查询是否已存在
    Mapper-->>Service: List<RiskCustomerManage>
    
    alt 已存在且未过期
        Service->>Service: 更新现有记录
    else 不存在或已过期
        Service->>Service: 创建新记录
    end
    
    Service->>Service: 设置有效期
    Service->>Service: 填充订单信息
    
    Service->>Mapper: 插入或更新记录
    Mapper->>DB: 执行SQL
    DB-->>Mapper: 执行结果
    Mapper-->>Service: 操作结果
    
    Service-->>Controller: UiResult
    Controller-->>Client: UiResultWrapper
```

### 5.2 客服拉黑司机

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as BlackListController
    participant Service as BlackListService
    participant Mapper as RiskCustomerManageMapper
    participant DB as 数据库
    
    Client->>Controller: POST /manage/blackList/driver/black
    Controller->>Service: blackDriverFromManage(request)
    
    loop 遍历黑名单列表
        Service->>Service: 获取分布式锁
        Service->>Service: 检查是否存在白名单
        
        alt 存在白名单
            Service->>Service: 跳过处理
        else 不存在白名单
            Service->>Mapper: 查询是否已存在
            Mapper-->>Service: List<RiskCustomerManage>
            
            alt 全部拉黑类型
                Service->>Service: 处理全部拉黑逻辑
            else 一对一拉黑类型
                Service->>Service: 处理一对一拉黑逻辑
            end
            
            Service->>Mapper: 插入或更新记录
            Mapper->>DB: 执行SQL
        end
        
        Service->>Service: 释放分布式锁
    end
    
    Service-->>Controller: UiResult
    Controller-->>Client: UiResultWrapper
```

### 5.3 外部系统同步黑名单

```mermaid
sequenceDiagram
    participant Client as 外部系统
    participant Controller as BlackListApiController
    participant Service as BlackListService
    participant Mapper as RiskCustomerManageMapper
    participant DB as 数据库
    
    Client->>Controller: POST /blackList/sync
    Controller->>Service: syncDriverBlack(request)
    
    Service->>Service: 参数校验
    
    loop 遍历黑名单列表
        Service->>Service: 设置风险类型
        Service->>Mapper: 查询是否已存在
        Mapper-->>Service: List<RiskCustomerManage>
        
        alt 操作类型为新增/更新
            Service->>Service: 处理新增/更新逻辑
        else 操作类型为删除
            Service->>Service: 处理删除逻辑
        end
        
        Service->>Mapper: 插入、更新或标记删除
        Mapper->>DB: 执行SQL
    end
    
    Service-->>Controller: UiResult
    Controller-->>Client: UiResultWrapper
```

## 6. 黑名单移除流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as BlackListApiController
    participant Service as BlackListService
    participant Mapper as RiskCustomerManageMapper
    participant DB as 数据库
    
    Client->>Controller: POST /blackList/driver/remove
    Controller->>Service: removeDriver(request)
    
    Service->>Mapper: 查询黑名单记录
    Mapper-->>Service: RiskCustomerManage
    
    alt 记录存在
        Service->>Service: 设置状态为失效
        Service->>Service: 设置失效时间为当前时间
        Service->>Mapper: 更新记录
        Mapper->>DB: 执行SQL
    end
    
    Service-->>Controller: UiResult
    Controller-->>Client: UiResultWrapper
```

## 7. 黑名单查询流程

### 7.1 查询用户拉黑司机列表

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as BlackListApiController
    participant Service as BlackListService
    participant Mapper as RiskCustomerManageMapper
    participant DB as 数据库
    
    Client->>Controller: POST /blackList/driver/list
    Controller->>Service: listDriver(request)
    
    Service->>Mapper: 查询黑名单记录
    Mapper->>DB: 执行SQL
    DB-->>Mapper: 查询结果
    Mapper-->>Service: List<RiskCustomerManage>
    
    Service->>Service: 过滤和转换数据
    Service-->>Controller: UiResult<List<RiskCustomerManageListDTO>>
    Controller-->>Client: UiResultWrapper
```

### 7.2 检查司机是否被拉黑

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as BlackListApiController
    participant Service as BlackListService
    participant Mapper as RiskCustomerManageMapper
    participant DB as 数据库
    
    Client->>Controller: POST /blackList/driver/checkIn
    Controller->>Service: checkDriverIn(request)
    
    Service->>Mapper: 查询黑名单记录
    Mapper->>DB: 执行SQL
    DB-->>Mapper: 查询结果
    Mapper-->>Service: List<RiskCustomerManage>
    
    alt 记录存在
        Service-->>Controller: UiResult(true)
    else 记录不存在
        Service-->>Controller: UiResult(false)
    end
    
    Controller-->>Client: UiResultWrapper
```

### 7.3 批量查询车牌黑名单

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as BlackListApiController
    participant Service as BlackListService
    participant Mapper as RiskCustomerManageMapper
    participant DB as 数据库
    
    Client->>Controller: POST /blackList/batchQueryDriverBlack
    Controller->>Service: batchQueryDriverBlack(request)
    
    Service->>Service: 参数校验
    Service->>Mapper: 查询黑名单记录
    Mapper->>DB: 执行SQL
    DB-->>Mapper: 查询结果
    Mapper-->>Service: List<RiskCustomerManage>
    
    Service->>Service: 筛选有效白名单
    Service->>Service: 筛选黑名单且不在白名单中的记录
    Service->>Service: 转换为CarBlackInfo对象
    
    Service-->>Controller: List<CarBlackInfo>
    Controller-->>Client: UiResultWrapper
```

## 8. 数据表关系

```mermaid
erDiagram
    risk_customer_manage ||--o{ risk_customer_record : "记录操作"
    
    risk_customer_manage {
        bigint id PK
        int customer_type
        string customer_value
        int risk_type
        int status
        int ttl
        datetime create_time
        datetime update_time
        datetime invalid_time
        string bind_user
        string bind_order
    }
    
    risk_customer_record {
        bigint id PK
        bigint customer_id FK
        int operate_type
        string create_user
        string operate_user
        int customer_type
        string remark
        datetime create_time
        string customer_value
    }
```

## 9. 黑名单功能入口汇总

### 9.1 添加黑名单入口

| 接口路径 | 控制器 | 服务方法 | 描述 |
| --- | --- | --- | --- |
| `/blackList/driver/black` | BlackListApiController | blackDriver | 用户拉黑司机 |
| `/manage/blackList/driver/black` | BlackListController | blackDriverFromManage | 客服拉黑司机 |
| `/blackList/sync` | BlackListApiController | syncDriverBlack | 外部系统同步黑名单 |
| 定时任务 | RiskDriverTask | - | 系统自动拉黑 |

### 9.2 查询黑名单入口

| 接口路径 | 控制器 | 服务方法 | 描述 |
| --- | --- | --- | --- |
| `/blackList/driver/list` | BlackListApiController | listDriver | 查询用户拉黑司机列表 |
| `/blackList/driver/checkIn` | BlackListApiController | checkDriverIn | 检查司机是否被拉黑 |
| `/blackList/batchQueryDriverBlack` | BlackListApiController | batchQueryDriverBlack | 批量查询车牌黑名单 |
| `/blackDriver/getList` | BlackDriverShieldController | getList | 查询用户拉黑司机列表（旧接口） |
| `/blackDriver/orderBlack` | BlackDriverShieldController | orderBlack | 检查订单是否被拉黑 |
| `/queryBlackDriver` | AdminPortalController | queryBlackDriver | 管理端查询黑名单司机 |

### 9.3 移除黑名单入口

| 接口路径 | 控制器 | 服务方法 | 描述 |
| --- | --- | --- | --- |
| `/blackList/driver/remove` | BlackListApiController | removeDriver | 用户取消拉黑司机 |
| `/blackDriver/userRemove` | BlackDriverShieldController | userRemove | 用户取消拉黑司机（旧接口） |

## 10. 总结

黑名单功能是风控系统的重要组成部分，通过多种方式管理和控制不同类型的风险用户和司机。系统支持全局黑名单和一对一黑名单等多种类型，并提供添加、查询、移除和同步等功能。

`/blackList/driver/list` 接口是黑名单功能中的查询接口，用于查询用户拉黑的司机列表。该接口通过查询 `risk_customer_manage` 表中与指定乘客手机号绑定且未过期的记录，返回已被拉黑的司机信息。
