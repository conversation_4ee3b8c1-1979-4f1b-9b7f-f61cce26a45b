# risk_customer_manage表业务流程分析 - 01 项目总览和架构

## 1. 项目概述

car-risk项目是一个基于Spring Boot的分布式风控系统，主要用于处理车辆相关业务的风险控制。该系统采用多模块架构设计，核心围绕`risk_customer_manage`表进行风控名单管理。

### 1.1 项目模块结构

```
car-risk/
├── car-risk-api/          # API接口定义模块
├── car-risk-common/       # 公共组件模块
├── car-risk-impl/         # 实现模块
├── car-risk-manage/       # 管理端模块（后台管理）
└── car-risk-process/      # 处理端模块（业务处理）
```

### 1.2 核心模块功能

#### car-risk-manage模块
- **功能定位**：风控名单的后台管理功能
- **主要职责**：
  - 风控名单的CRUD操作
  - Excel批量导入/导出
  - 名单查询和统计
  - 管理员操作界面
- **部署方式**：独立部署的管理后台服务

#### car-risk-process模块  
- **功能定位**：风控名单的业务处理和应用
- **主要职责**：
  - 风控规则引擎查询
  - 定时任务处理
  - 对外API服务
  - 缓存管理
- **部署方式**：独立部署的业务处理服务

## 2. risk_customer_manage表核心地位

### 2.1 表结构概览

`risk_customer_manage`表是整个风控系统的核心数据表，存储所有类型的风控客户名单信息。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| id | Long | 主键ID | 唯一标识 |
| customer_type | Integer | 客户类型 | 1-用户ID，2-用户手机号，6-司机车牌号，10-身份证号等 |
| customer_value | String | 客户值 | 具体的标识值（车牌号、手机号等） |
| risk_type | Integer | 风险类型 | 1-黑名单，2-白名单，7-一对一名单等 |
| status | Integer | 状态 | 1-有效，2-失效，3-已删除 |
| ttl | Integer | 有效期限 | 天数，-1表示永久有效 |
| create_time | Date | 创建时间 | 记录创建时间 |
| update_time | Date | 更新时间 | 最后更新时间 |
| invalid_time | Date | 失效时间 | 计算得出的失效时间点 |
| option_type | Integer | 操作类型 | 1-系统操作，2-人工操作，3-客户操作 |
| create_user | String | 创建人 | 创建记录的用户 |
| option_name | String | 操作人 | 最后操作的用户 |
| risk_remark | String | 风险备注 | 拉黑原因或备注信息 |
| bind_user | String | 绑定用户 | 一对一名单中的乘客手机号 |
| bind_order | String | 绑定订单 | 关联的订单号 |
| supplier_name | String | 供应商名称 | 供应商信息 |
| member_id | String | 会员ID | 用户会员标识 |
| driver_id | String | 司机ID | 司机标识 |

### 2.2 数据流向架构

```mermaid
graph TB
    A[管理后台用户] --> B[car-risk-manage模块]
    C[业务系统调用] --> D[car-risk-process模块]
    E[定时任务] --> D
    F[规则引擎] --> D
    
    B --> G[RiskCustomerManageMapper]
    D --> G
    
    G --> H[risk_customer_manage表]
    
    H --> I[风控查询服务]
    H --> J[Redis缓存]
    H --> K[操作记录表]
    
    I --> L[营销风控]
    I --> M[司机接单检查]
    I --> N[用户下单检查]
```

## 3. 系统架构设计

### 3.1 技术栈

- **框架**：Spring Boot 2.7.3
- **数据库**：MySQL（分库分表）
- **ORM**：MyBatis Plus
- **缓存**：Redis + Redisson
- **消息队列**：Kafka
- **配置中心**：自研配置中心
- **监控**：自研监控系统

### 3.2 部署架构

```mermaid
graph TB
    A[负载均衡器] --> B[car-risk-manage集群]
    A --> C[car-risk-process集群]
    
    B --> D[MySQL主库]
    C --> D
    C --> E[MySQL从库]
    
    B --> F[Redis集群]
    C --> F
    
    C --> G[Kafka集群]
    
    H[配置中心] --> B
    H --> C
```

### 3.3 数据库设计

#### 主要相关表结构

1. **risk_customer_manage** - 风控客户管理主表
2. **risk_customer_record** - 风控客户操作记录表
3. **hc_customer** - 汇川客户表（司机端）
4. **metric_strategy** - 风控策略表
5. **risk_hit** - 风控命中记录表

#### 分库分表策略

- **分库**：按业务线分库（顺风车、网约车等）
- **分表**：按时间分表（按月分表）
- **路由规则**：根据customer_value进行hash路由

## 4. 核心业务场景

### 4.1 风控名单管理场景

#### 4.1.1 手动管理
- 客服人员通过管理后台添加/删除风控名单
- 支持单个添加和批量Excel导入
- 提供查询、导出、统计功能

#### 4.1.2 自动化管理
- 规则引擎自动拉黑风险用户
- 定时任务自动失效过期名单
- 系统间数据同步

### 4.2 风控检查场景

#### 4.2.1 实时风控检查
- 用户下单时检查是否在风控名单中
- 司机接单时检查是否在黑名单中
- 营销活动时检查用户风控状态

#### 4.2.2 批量风控查询
- 支持批量查询多个用户的风控状态
- 提供缓存机制提升查询性能
- 支持多维度查询（手机号、车牌号、身份证等）

### 4.3 数据同步场景

#### 4.3.1 上游系统同步
- 接收业务系统的风控名单同步请求
- 处理司机黑名单数据同步
- 汇川系统数据同步

#### 4.3.2 下游系统推送
- 向业务系统推送风控名单变更
- 缓存更新通知
- 实时风控状态推送

## 5. 关键技术特性

### 5.1 高可用设计

- **服务集群**：多实例部署，支持水平扩展
- **数据库**：主从复制，读写分离
- **缓存**：Redis集群，数据冗余
- **降级策略**：缓存降级，默认策略

### 5.2 性能优化

- **缓存策略**：多级缓存，热点数据预加载
- **数据库优化**：索引优化，分库分表
- **异步处理**：消息队列异步处理
- **批量操作**：批量查询，批量更新

### 5.3 数据一致性

- **事务管理**：分布式事务处理
- **数据同步**：最终一致性保证
- **冲突处理**：乐观锁，版本控制
- **数据校验**：定时数据一致性检查

## 6. 监控和运维

### 6.1 监控指标

- **业务指标**：风控命中率，处理延迟
- **系统指标**：CPU、内存、网络
- **数据库指标**：连接数，慢查询
- **缓存指标**：命中率，内存使用

### 6.2 告警机制

- **业务告警**：风控规则异常，数据同步失败
- **系统告警**：服务不可用，资源不足
- **数据告警**：数据不一致，性能异常

### 6.3 运维工具

- **配置管理**：动态配置更新
- **日志管理**：集中日志收集和分析
- **部署工具**：自动化部署和回滚
- **数据工具**：数据迁移和备份

## 7. 安全设计

### 7.1 数据安全

- **敏感数据脱敏**：手机号、身份证号脱敏显示
- **数据加密**：敏感字段加密存储
- **访问控制**：基于角色的权限控制
- **审计日志**：操作记录完整追踪

### 7.2 接口安全

- **身份认证**：OAuth2.0认证机制
- **权限控制**：细粒度权限管理
- **接口限流**：防止恶意调用
- **数据校验**：输入参数严格校验

## 8. 扩展性设计

### 8.1 模块化设计

- **服务拆分**：按业务功能拆分服务
- **接口标准化**：统一的API设计规范
- **配置外化**：配置与代码分离
- **插件化**：支持功能插件扩展

### 8.2 数据扩展

- **表结构扩展**：预留扩展字段
- **索引优化**：支持新查询场景
- **分片扩展**：支持数据量增长
- **存储扩展**：支持多种存储引擎

这个总览为后续详细分析奠定了基础，接下来将深入分析各个层次的具体实现。
