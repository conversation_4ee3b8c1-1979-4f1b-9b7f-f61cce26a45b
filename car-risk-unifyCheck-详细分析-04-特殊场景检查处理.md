# car-risk项目 `/riskCheck/unifyCheck` 接口详细分析 - 第4部分：特殊场景检查处理

## 1. 特殊场景检查总体架构

### 1.1 特殊场景检查在整体流程中的位置

特殊场景检查位于黑名单检查之后，策略检查之前，用于处理一些需要特殊业务逻辑的场景：

```java
public RiskSceneResult unifyCheck(UnifyCheckRequest request) throws BizException {
    // 1. 参数校验
    paramCheck(request);

    // 2. 黑名单检查
    RiskSceneResult blackListCheckResult = blackListHandler.blackListCheck(request);
    if(null != blackListCheckResult){
        return blackListCheckResult;
    }

    // 3. 分销单判断
    if (request.isDistributionFlag()) {
        return RiskSceneResult.pass("分销单判定风险名单通过");
    }

    // 4. 特殊场景检查
    RiskSceneResult specialSceneCheck = specialHandlerCheck(request);
    if (null != specialSceneCheck && specialSceneCheck.isRiskFlag()) {
        return specialSceneCheck;  // 命中特殊场景规则直接返回
    }

    // 5. 后续策略检查...
}
```

### 1.2 特殊场景处理器架构

特殊场景检查采用工厂模式 + 策略模式的设计：

```
SpecialSceneHandlerFactory (工厂类)
├── BankCardChangeSceneHandler (银行卡变更场景处理器)
├── DriverAuthenticationSceneHandler (司机认证场景处理器)
└── 其他特殊场景处理器...
```

## 2. 特殊场景处理工厂

### 2.1 SpecialSceneHandlerFactory详细分析

**文件位置**: `car-risk-process/src/main/java/com/ly/car/risk/process/scene/SpecialSceneHandlerFactory.java`

```java
@Service
public class SpecialSceneHandlerFactory implements ApplicationContextAware, InitializingBean {

    private ApplicationContext ctx;

    // 场景与处理器的映射关系
    private ConcurrentHashMap<StrategySceneEnum, List<SpecialSceneHandler>> handlerMaps = new ConcurrentHashMap<>();

    /**
     * 特殊场景检查入口
     */
    public RiskSceneResult handlerCheck(UnifyCheckRequest request){
        StrategySceneEnum sceneEnum = StrategySceneEnum.of(request.getScene());
        List<SpecialSceneHandler> specialSceneHandlers = handlerMaps.get(sceneEnum);
        
        if(CollUtil.isEmpty(specialSceneHandlers)){
            return null;  // 该场景没有特殊处理器
        }
        
        // 遍历该场景的所有处理器
        for(SpecialSceneHandler handler : specialSceneHandlers){
            RiskSceneResult result = handler.check(request);
            if(null != result && result.isRiskFlag()){
                return result;  // 命中风险规则，直接返回
            }
        }
        return null;  // 未命中任何特殊规则
    }

    /**
     * 初始化处理器映射关系
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        // 从Spring容器中获取所有SpecialSceneHandler实现类
        Map<String, SpecialSceneHandler> handlers = ctx.getBeansOfType(SpecialSceneHandler.class, false, true);
        ConcurrentHashMap<StrategySceneEnum, List<SpecialSceneHandler>> map = new ConcurrentHashMap<>();
        
        for (SpecialSceneHandler handler : handlers.values()) {
            List<StrategySceneEnum> strategySceneEnums = handler.supportScene();
            for(StrategySceneEnum sceneEnum : strategySceneEnums){
                if(map.containsKey(sceneEnum)){
                    map.get(sceneEnum).add(handler);
                }else{
                    List<SpecialSceneHandler> list = new ArrayList<>();
                    list.add(handler);
                    map.put(sceneEnum, list);
                }
            }
        }
        this.handlerMaps = map;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.ctx = applicationContext;
    }
}
```

### 2.2 SpecialSceneHandler接口定义

```java
public interface SpecialSceneHandler {
    
    /**
     * 支持的场景列表
     */
    List<StrategySceneEnum> supportScene();
    
    /**
     * 特殊场景检查逻辑
     */
    RiskSceneResult check(UnifyCheckRequest request);
}
```

## 3. 银行卡变更场景处理器

### 3.1 BankCardChangeSceneHandler详细分析

**文件位置**: `car-risk-process/src/main/java/com/ly/car/risk/process/scene/BankCardChangeSceneHandler.java`

```java
@Component
@Slf4j
public class BankCardChangeSceneHandler implements SpecialSceneHandler {

    @Resource
    private RiskCustomerService riskCustomerService;

    @Override
    public List<StrategySceneEnum> supportScene() {
        // 支持银行卡变更场景
        return Arrays.asList(StrategySceneEnum.BANK_CARD_CHANGE);
    }

    @Override
    public RiskSceneResult check(UnifyCheckRequest request) {
        try {
            // 银行卡变更场景的特殊检查逻辑
            return checkBankCardChange(request);
        } catch (Exception e) {
            log.error("银行卡变更场景检查异常", e);
            return null;
        }
    }

    /**
     * 银行卡变更特殊检查逻辑
     */
    private RiskSceneResult checkBankCardChange(UnifyCheckRequest request) {
        // 1. 获取用户相关的风控记录
        FilterParams params = new FilterParams();
        params.setMemberId(request.getMemberId());
        params.setUserPhone(request.getUserPhone());
        
        List<RiskCustomerManage> riskCustomers = riskCustomerService.getListByValue(params, new Date());
        
        // 2. 检查是否存在禁止银行卡变更的记录
        boolean hasBankCardRisk = riskCustomers.stream()
                .anyMatch(customer -> 
                    customer.getRiskType().equals(RiskCustomerRiskTypeEnum.ban_bank_card_change.getCode())
                    && customer.getStatus().equals(RiskCustomerStatusEnum.valid.getCode())
                    && customer.getInvalidTime().after(new Date()));
        
        if (hasBankCardRisk) {
            return RiskSceneResult.fail("用户存在银行卡变更风险，禁止操作");
        }
        
        // 3. 检查银行卡变更频率
        long recentChangeCount = riskCustomers.stream()
                .filter(customer -> 
                    customer.getCustomerType().equals(RiskCustomerCustomerTypeEnum.pay_account.getCode())
                    && customer.getCreateTime().after(DateUtils.addDays(new Date(), -30)))
                .count();
        
        if (recentChangeCount >= 3) {
            return RiskSceneResult.fail("30天内银行卡变更次数过多，禁止操作");
        }
        
        return null; // 通过特殊检查
    }
}
```

## 4. 司机认证场景处理器

### 4.1 DriverAuthenticationSceneHandler详细分析

**文件位置**: `car-risk-process/src/main/java/com/ly/car/risk/process/scene/DriverAuthenticationSceneHandler.java`

```java
@Component
@Slf4j
public class DriverAuthenticationSceneHandler implements SpecialSceneHandler {

    @Resource
    private RiskCustomerService riskCustomerService;
    
    @Resource
    private ThirdRiskClient thirdRiskClient;

    @Override
    public List<StrategySceneEnum> supportScene() {
        // 支持司机认证场景
        return Arrays.asList(StrategySceneEnum.DRIVER_AUTHENTICATION);
    }

    @Override
    public RiskSceneResult check(UnifyCheckRequest request) {
        try {
            // 司机认证场景的特殊检查逻辑
            return checkDriverAuthentication(request);
        } catch (Exception e) {
            log.error("司机认证场景检查异常", e);
            return null;
        }
    }

    /**
     * 司机认证特殊检查逻辑
     */
    private RiskSceneResult checkDriverAuthentication(UnifyCheckRequest request) {
        
        // 1. 检查司机是否在禁止认证名单中
        String driverId = StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.DRIVER_ID);
        String certNo = StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.CERT_NO);
        
        if (StringUtils.isBlank(driverId) || StringUtils.isBlank(certNo)) {
            return RiskSceneResult.fail("司机认证参数不完整");
        }
        
        // 2. 查询禁止认证名单
        FilterParams params = new FilterParams();
        params.setDriverId(driverId);
        params.setIdCardNo(certNo);
        
        List<RiskCustomerManage> riskCustomers = riskCustomerService.getListByValue(params, new Date());
        
        boolean hasBanRegister = riskCustomers.stream()
                .anyMatch(customer -> 
                    customer.getRiskType().equals(RiskCustomerRiskTypeEnum.ban_register_list.getCode())
                    && customer.getStatus().equals(RiskCustomerStatusEnum.valid.getCode())
                    && customer.getInvalidTime().after(new Date()));
        
        if (hasBanRegister) {
            return RiskSceneResult.fail("司机在禁止认证名单中，认证失败");
        }
        
        // 3. 第三方风控检查（如果配置了）
        if (isThirdRiskCheckEnabled()) {
            ThirdRiskCheckResult thirdResult = thirdRiskClient.checkDriverRisk(driverId, certNo);
            if (thirdResult != null && thirdResult.isRisk()) {
                return RiskSceneResult.fail("第三方风控检查未通过：" + thirdResult.getReason());
            }
        }
        
        // 4. 检查认证频率
        long recentAuthCount = riskCustomers.stream()
                .filter(customer -> 
                    customer.getCustomerType().equals(RiskCustomerCustomerTypeEnum.id_card_no.getCode())
                    && customer.getCreateTime().after(DateUtils.addDays(new Date(), -7)))
                .count();
        
        if (recentAuthCount >= 5) {
            return RiskSceneResult.fail("7天内认证次数过多，请稍后再试");
        }
        
        return null; // 通过特殊检查
    }
    
    private boolean isThirdRiskCheckEnabled() {
        // 从配置中心获取是否启用第三方风控检查
        return ConfigCenterClient.getBoolean("driver.auth.third.risk.check.enabled", false);
    }
}
```

## 5. 特殊场景检查流程图

```mermaid
graph TD
    A[开始特殊场景检查] --> B[根据场景获取处理器列表]
    B --> C{是否有处理器?}
    C -->|否| D[返回null，继续后续检查]
    C -->|是| E[遍历处理器列表]
    
    E --> F[执行处理器检查逻辑]
    F --> G{检查结果是否为风险?}
    G -->|是| H[返回风险结果，终止检查]
    G -->|否| I{是否还有其他处理器?}
    I -->|是| E
    I -->|否| J[返回null，继续后续检查]
    
    subgraph "银行卡变更场景检查"
        K[检查禁止银行卡变更名单]
        L[检查银行卡变更频率]
        M[第三方风控检查]
    end
    
    subgraph "司机认证场景检查"
        N[检查禁止认证名单]
        O[检查认证频率]
        P[第三方风控检查]
    end
    
    F --> K
    F --> N
```

## 6. 扩展参数处理

### 6.1 UnifyReqExtConst常量定义

```java
public class UnifyReqExtConst {
    public static final String DRIVER_ID = "driverId";
    public static final String CERT_NO = "certNo";
    public static final String BANK_CARD_NO = "bankCardNo";
    public static final String OLD_BANK_CARD_NO = "oldBankCardNo";
    public static final String AUTH_TYPE = "authType";
    public static final String DEVICE_INFO = "deviceInfo";
    // ... 其他扩展参数常量
}
```

### 6.2 扩展参数获取工具

```java
public class StrategyUtil {
    
    /**
     * 从扩展参数中获取字符串值
     */
    public static String getStringFromExt(Map<String, Object> ext, String key) {
        if (ext == null || !ext.containsKey(key)) {
            return StringUtils.EMPTY;
        }
        Object value = ext.get(key);
        return value == null ? StringUtils.EMPTY : String.valueOf(value);
    }
    
    /**
     * 从扩展参数中获取整数值
     */
    public static Integer getIntegerFromExt(Map<String, Object> ext, String key) {
        String value = getStringFromExt(ext, key);
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return Integer.valueOf(value);
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
```

## 7. 特殊场景配置管理

### 7.1 配置中心集成

特殊场景处理器支持通过配置中心动态调整检查规则：

```java
public class SceneConfigManager {
    
    /**
     * 获取银行卡变更频率限制
     */
    public static int getBankCardChangeLimit() {
        return ConfigCenterClient.getInt("bank.card.change.limit.30days", 3);
    }
    
    /**
     * 获取司机认证频率限制
     */
    public static int getDriverAuthLimit() {
        return ConfigCenterClient.getInt("driver.auth.limit.7days", 5);
    }
    
    /**
     * 是否启用第三方风控检查
     */
    public static boolean isThirdRiskCheckEnabled() {
        return ConfigCenterClient.getBoolean("third.risk.check.enabled", false);
    }
}
```

### 7.2 动态开关控制

```java
@Component
public class SceneSwitchManager {
    
    /**
     * 检查特殊场景是否启用
     */
    public boolean isSceneEnabled(StrategySceneEnum scene) {
        String configKey = "scene." + scene.getScene() + ".enabled";
        return ConfigCenterClient.getBoolean(configKey, true);
    }
    
    /**
     * 获取场景风险阈值
     */
    public double getSceneRiskThreshold(StrategySceneEnum scene) {
        String configKey = "scene." + scene.getScene() + ".risk.threshold";
        return ConfigCenterClient.getDouble(configKey, 0.8);
    }
}
```

## 8. 新增特殊场景处理器示例

### 8.1 创建新的场景处理器

```java
@Component
@Slf4j
public class CouponReceiveSceneHandler implements SpecialSceneHandler {

    @Resource
    private RiskCustomerService riskCustomerService;

    @Override
    public List<StrategySceneEnum> supportScene() {
        // 支持活动领券场景
        return Arrays.asList(StrategySceneEnum.ACT_COUPON_LQ);
    }

    @Override
    public RiskSceneResult check(UnifyCheckRequest request) {
        try {
            return checkCouponReceive(request);
        } catch (Exception e) {
            log.error("领券场景检查异常", e);
            return null;
        }
    }

    private RiskSceneResult checkCouponReceive(UnifyCheckRequest request) {
        // 1. 检查是否在禁止领券名单中
        FilterParams params = new FilterParams();
        params.setMemberId(request.getMemberId());
        params.setUnionId(request.getUnionId());
        
        List<RiskCustomerManage> riskCustomers = riskCustomerService.getListByValue(params, new Date());
        
        boolean hasBanCoupon = riskCustomers.stream()
                .anyMatch(customer -> 
                    customer.getRiskType().equals(RiskCustomerRiskTypeEnum.ban_coupon_list.getCode())
                    && customer.getStatus().equals(RiskCustomerStatusEnum.valid.getCode())
                    && customer.getInvalidTime().after(new Date()));
        
        if (hasBanCoupon) {
            return RiskSceneResult.fail("用户在禁止领券名单中");
        }
        
        // 2. 检查领券频率
        String activityId = StrategyUtil.getStringFromExt(request.getExt(), "activityId");
        if (StringUtils.isNotBlank(activityId)) {
            // 检查该活动的领券频率
            // ... 具体逻辑
        }
        
        return null;
    }
}
```

### 8.2 自动注册机制

由于使用了Spring的自动扫描和`SpecialSceneHandlerFactory`的初始化机制，新增的处理器会自动被注册到工厂中，无需额外配置。

## 9. 总结

特殊场景检查模块的设计具有以下特点：

1. **可扩展性**: 通过接口和工厂模式，可以轻松添加新的场景处理器
2. **灵活性**: 每个场景可以有多个处理器，支持复杂的业务逻辑
3. **可配置性**: 通过配置中心支持动态调整检查规则
4. **高性能**: 只有配置了特殊处理器的场景才会执行额外检查
5. **容错性**: 单个处理器异常不会影响整体流程

这种设计使得系统能够灵活应对各种特殊业务场景的风控需求，同时保持了代码的清晰和可维护性。
