# risk_customer_manage表业务流程分析 - 02 数据访问层详细分析

## 1. Mapper层架构概述

risk_customer_manage表的数据访问层采用MyBatis Plus框架，分别在car-risk-manage和car-risk-process两个模块中实现，各自承担不同的业务职责。

### 1.1 模块分工

| 模块 | 职责 | 主要操作类型 |
|------|------|-------------|
| car-risk-manage | 管理端数据操作 | CRUD、批量操作、统计查询 |
| car-risk-process | 业务端数据操作 | 查询、更新、定时任务 |

## 2. car-risk-manage模块数据访问层

### 2.1 RiskCustomerManageMapper接口定义

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/repo/risk/mapper/RiskCustomerManageMapper.java" mode="EXCERPT">
````java
public interface RiskCustomerManageMapper extends BaseMapper<RiskCustomerManage> {
    // 分页查询相关
    long getListTotal(Object query);
    List<RiskCustomerManage> getList(Object query);
    List<RiskCustomerManage> getListExport(Object query);
    
    // 业务查询方法
    RiskCustomerManage getByTypeAndValueAndRiskType(@Param("customerType") Integer customerType,
                                                    @Param("customerValue") String customerValue,
                                                    @Param("riskType") Integer riskType,
                                                    @Param("time")Date time);
    
    RiskCustomerManage getByTypeAndValueAndRiskTypeAndUser(@Param("customerType") Integer customerType,
                                                    @Param("customerValue") String customerValue,
                                                    @Param("riskType") Integer riskType,
                                                    @Param("bindUser") String bindUser,
                                                    @Param("time")Date time);
    
    // 数据清理方法
    int clearInvalid();
    int clearInvalidById(@Param("id")Long id);
    
    // 重复数据检查
    List<RiskCustomerManage> getDupList();
    List<RiskCustomerManage> getDup1v1List();
    
    // 条件查询
    List<RiskCustomerManage> getByCondition(Object query);
}
````
</augment_code_snippet>

### 2.2 SQL实现详细分析

#### 2.2.1 分页查询实现

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/repo/risk/mapper/RiskCustomerManageMapper.xml" mode="EXCERPT">
````xml
<select id="getList" resultType="com.ly.car.risk.entity.RiskCustomerManage">
    select * from risk_customer_manage
    <include refid="getListCondition"/>
    order by id desc
    limit #{offset}, #{size}
</select>

<select id="getListTotal" resultType="long">
    select count(*) from risk_customer_manage
    <include refid="getListCondition"/>
</select>
````
</augment_code_snippet>

**查询条件SQL片段分析：**

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/repo/risk/mapper/RiskCustomerManageMapper.xml" mode="EXCERPT">
````xml
<sql id="getListCondition">
    where 1=1
    <if test="status != null ">
        and status = #{status}
    </if>
    <if test="customerType != null  ">
       and customer_type = #{customerType}
    </if>
    <if test="customerValue != null  and customerValue != '' ">
        and customer_value = #{customerValue}
    </if>
    <if test="riskType != null">
        and risk_type = #{riskType}
    </if>
    <!-- 有效状态时检查失效时间 -->
    <if test="status != null and status == 1 ">
        and  invalid_time &gt; #{invalidTime}
    </if>
    <!-- 失效状态时检查失效时间 -->
    <if test="status != null and status == 2 ">
        and  invalid_time &lt; #{invalidTime}
    </if>
    <!-- 其他查询条件... -->
</sql>
````
</augment_code_snippet>

#### 2.2.2 核心业务查询方法

**按类型、值和风险类型查询（普通名单）：**

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/repo/risk/mapper/RiskCustomerManageMapper.xml" mode="EXCERPT">
````xml
<select id="getByTypeAndValueAndRiskType" resultType="com.ly.car.risk.entity.RiskCustomerManage">
    select * from risk_customer_manage where
        customer_type = #{customerType} and
        customer_value = #{customerValue} and
        risk_type = #{riskType} and
        invalid_time &gt; #{time}
         and status = 1
    order by create_time desc limit 1
</select>
````
</augment_code_snippet>

**按类型、值、风险类型和绑定用户查询（一对一名单）：**

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/repo/risk/mapper/RiskCustomerManageMapper.xml" mode="EXCERPT">
````xml
<select id="getByTypeAndValueAndRiskTypeAndUser" resultType="com.ly.car.risk.entity.RiskCustomerManage">
    select * from risk_customer_manage where
        customer_type = #{customerType} and
        customer_value = #{customerValue} and
        risk_type = #{riskType} and
        bind_user = #{bindUser} and
        invalid_time &gt; #{time}
         and status = 1
    order by create_time desc limit 1
</select>
````
</augment_code_snippet>

#### 2.2.3 数据清理和维护

**批量清理失效数据：**

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/repo/risk/mapper/RiskCustomerManageMapper.xml" mode="EXCERPT">
````xml
<update id="clearInvalid">
    update risk_customer_manage
    set update_time = CURRENT_TIMESTAMP,
        status = 3,
        option_type=1,
        option_name = '系统操作'
     where status=2 or invalid_time &lt; NOW()
</update>
````
</augment_code_snippet>

**按ID清理单条数据：**

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/repo/risk/mapper/RiskCustomerManageMapper.xml" mode="EXCERPT">
````xml
<update id="clearInvalidById">
    update risk_customer_manage
    set update_time = CURRENT_TIMESTAMP,
        status      = 3,
        option_type=1,
        option_name = '系统操作'
    where id = #{id}
</update>
````
</augment_code_snippet>

#### 2.2.4 重复数据检查

**检查非一对一名单的重复数据：**

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/repo/risk/mapper/RiskCustomerManageMapper.xml" mode="EXCERPT">
````xml
<select id="getDupList" resultType="com.ly.car.risk.entity.RiskCustomerManage">
    select risk_type, customer_type, customer_value
    from risk_customer_manage
    WHERE status = 1 and risk_type != 7
    GROUP BY risk_type, customer_type, customer_value
    HAVING COUNT(*) > 1
</select>
````
</augment_code_snippet>

**检查一对一名单的重复数据：**

<augment_code_snippet path="car-risk-manage/src/main/java/com/ly/car/risk/manage/repo/risk/mapper/RiskCustomerManageMapper.xml" mode="EXCERPT">
````xml
<select id="getDup1v1List" resultType="com.ly.car.risk.entity.RiskCustomerManage">
    select risk_type, customer_type, customer_value,bind_user
    from risk_customer_manage
    WHERE status = 1 and risk_type = 7
    GROUP BY risk_type, customer_type, customer_value, bind_user
    HAVING COUNT(*) > 1
</select>
````
</augment_code_snippet>

## 3. car-risk-process模块数据访问层

### 3.1 RiskCustomerManageMapper接口定义

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/repo/risk/mapper/RiskCustomerManageMapper.java" mode="EXCERPT">
````java
public interface RiskCustomerManageMapper extends BaseMapper<RiskCustomerManage> {
    
    // 定时任务相关
    @Select("select * from risk_customer_manage where status = 1 and ...")
    List<RiskCustomerManage> selectInvalidData();
    
    // 业务查询方法
    List<RiskCustomerManage> getListByValue(@Param("params")FilterParams params, 
                                           @Param("invalidTime")Date dateTime);
    
    List<RiskCustomerManage> getListByValueByGroup(@Param("params") CommonCustomerParam params, 
                                                  @Param("invalidTime")Date dateTime);
    
    // 统计查询
    long getValidCount(@Param("customerType") int customerType,
                       @Param("customerValue") String customerValue,
                       @Param("riskType") int riskType);
    
    // 批量查询
    List<RiskCustomerManage> queryAllValidRiskRecord(@Param("offset") int offset, 
                                                     @Param("limit") int limit);
}
````
</augment_code_snippet>

### 3.2 业务查询SQL实现

#### 3.2.1 定时失效数据查询

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/repo/risk/mapper/RiskCustomerManageMapper.java" mode="EXCERPT">
````java
@Select("select "
        + "  * "
        + "from "
        + "  risk_customer_manage "
        + "where "
        + "  status = 1 "
        + "  and ( "
        + "    ( "
        + "      ttl != -1 "
        + "      and ttl != 0 "
        + "      and NOW() >= DATE_ADD(create_time, INTERVAL ttl day) "
        + "    ) "
        + "    or ( "
        + "      ttl = 0 "
        + "      and NOW() >= invalid_time "
        + "    ) "
        + "  )")
List<RiskCustomerManage> selectInvalidData();
````
</augment_code_snippet>

**SQL逻辑分析：**
1. 查询状态为有效(status=1)的记录
2. 满足以下任一条件：
   - ttl不为-1且不为0，且当前时间超过创建时间+ttl天数
   - ttl为0，且当前时间超过invalid_time

#### 3.2.2 多维度查询实现

**按多个字段值查询：**

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/repo/risk/mapper/RiskCustomerManageMapper.xml" mode="EXCERPT">
````xml
<select id="getListByValue" resultType="com.ly.car.risk.entity.RiskCustomerManage">
    select * from risk_customer_manage where invalid_time &gt; #{invalidTime} and (
    <trim prefixOverrides="or">
        <if test=" params.memberId != null and params.memberId != '' ">
            or customer_value = #{params.memberId}
        </if>
        <if test=" params.unionId != null and params.unionId != '' ">
            or customer_value = #{params.unionId}
        </if>
        <if test=" params.userPhone != null and params.userPhone != '' ">
            or customer_value = #{params.userPhone}
        </if>
        <if test=" params.driverCardNo != null and params.driverCardNo != '' ">
            or customer_value = #{params.driverCardNo}
        </if>
        <if test=" params.payAccount != null and params.payAccount != '' ">
            or customer_value = #{params.payAccount}
        </if>
        <if test=" params.passengerCellphone != null and params.passengerCellphone != '' ">
            or customer_value = #{params.passengerCellphone}
        </if>
    </trim>
    )
</select>
````
</augment_code_snippet>

**扩展的多维度查询（支持身份证批量查询）：**

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/repo/risk/mapper/RiskCustomerManageMapper.xml" mode="EXCERPT">
````xml
<select id="getListByValueByGroup" resultType="com.ly.car.risk.entity.RiskCustomerManage">
    select * from risk_customer_manage where invalid_time &gt; #{invalidTime} and (
    <trim prefixOverrides="or">
        <!-- 单个字段查询 -->
        <if test=" params.memberId != null and params.memberId != '' ">
            or customer_value = #{params.memberId}
        </if>
        <!-- ... 其他单个字段 ... -->
        
        <!-- 批量身份证号查询 -->
        <if test=" params.idCardNos != null and params.idCardNos.size >0 ">
            or ( customer_type = 10 and customer_value in
            <foreach collection="params.idCardNos" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
               )
        </if>
    </trim>
    )
</select>
````
</augment_code_snippet>

#### 3.2.3 统计和批量查询

**有效记录统计：**

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/repo/risk/mapper/RiskCustomerManageMapper.xml" mode="EXCERPT">
````xml
<select id="getValidCount" resultType="long">
    select count(*)
    from risk_customer_manage
    where customer_type = #{customerType}
      and customer_value = #{customerValue}
      and risk_type = #{riskType}
      and invalid_time > now()
      and status = 1
</select>
````
</augment_code_snippet>

**批量查询有效风控记录：**

<augment_code_snippet path="car-risk-process/src/main/java/com/ly/car/risk/process/repo/risk/mapper/RiskCustomerManageMapper.xml" mode="EXCERPT">
````xml
<select id="queryAllValidRiskRecord" resultType="com.ly.car.risk.entity.RiskCustomerManage">
    select * from risk_customer_manage
             where customer_type = 6
            and invalid_time > now()
            and risk_type in (1,6,7)
            and status = 1
            and customer_value !=''
            order by id
    limit #{limit} offset #{offset}
</select>
````
</augment_code_snippet>

## 4. 数据访问层设计模式分析

### 4.1 继承BaseMapper模式

两个模块的Mapper都继承了MyBatis Plus的BaseMapper，获得了基础的CRUD能力：

```java
public interface RiskCustomerManageMapper extends BaseMapper<RiskCustomerManage>
```

**提供的基础方法：**
- `insert(T entity)` - 插入记录
- `deleteById(Serializable id)` - 按ID删除
- `updateById(T entity)` - 按ID更新
- `selectById(Serializable id)` - 按ID查询
- `selectList(Wrapper<T> queryWrapper)` - 条件查询
- `selectCount(Wrapper<T> queryWrapper)` - 统计查询

### 4.2 自定义SQL扩展模式

针对复杂业务场景，通过XML配置自定义SQL：

1. **复杂条件查询** - 使用动态SQL处理多条件查询
2. **统计分析查询** - 使用聚合函数进行数据统计
3. **批量操作** - 使用批量更新和批量查询
4. **性能优化查询** - 使用索引优化和分页查询

### 4.3 参数传递模式

#### 4.3.1 对象参数传递
```java
List<RiskCustomerManage> getList(Object query);
```
使用查询对象封装复杂查询条件。

#### 4.3.2 @Param注解传递
```java
RiskCustomerManage getByTypeAndValueAndRiskType(@Param("customerType") Integer customerType,
                                                @Param("customerValue") String customerValue,
                                                @Param("riskType") Integer riskType,
                                                @Param("time")Date time);
```
使用@Param注解明确指定参数名称。

## 5. 索引设计和性能优化

### 5.1 核心索引设计

基于SQL查询分析，推荐的索引设计：

```sql
-- 主键索引
PRIMARY KEY (`id`)

-- 业务查询核心索引
KEY `idx_customer_type_value_risk_type` (`customer_type`, `customer_value`, `risk_type`)

-- 失效时间查询索引
KEY `idx_invalid_time_status` (`invalid_time`, `status`)

-- 一对一名单查询索引
KEY `idx_bind_user_customer_type` (`bind_user`, `customer_type`)

-- 创建时间索引（用于排序）
KEY `idx_create_time` (`create_time`)

-- 组合查询索引
KEY `idx_status_risk_type_invalid_time` (`status`, `risk_type`, `invalid_time`)
```

### 5.2 查询性能优化策略

#### 5.2.1 分页查询优化
- 使用limit进行分页，避免大结果集
- 通过索引覆盖减少回表查询
- 使用order by id desc利用主键索引排序

#### 5.2.2 条件查询优化
- 动态SQL避免不必要的条件判断
- 使用最左前缀原则优化复合索引
- 避免在where条件中使用函数

#### 5.2.3 批量操作优化
- 使用批量插入减少数据库交互
- 批量更新使用case when语句
- 大批量操作分批处理避免锁表

## 6. 数据一致性保证

### 6.1 事务管理

在Service层使用@Transactional注解保证数据一致性：

```java
@Transactional(rollbackFor = Exception.class)
public Boolean add(RiskCustomerAddParams params) {
    // 插入主表记录
    riskCustomerManageMapper.insert(entity);
    // 插入操作记录
    riskCustomerRecordMapper.insert(record);
}
```

### 6.2 并发控制

使用分布式锁防止并发操作冲突：

```java
RLock lock = redissonClient.getLock("risk_customer_" + customerValue);
try {
    if (lock.tryLock(10, TimeUnit.SECONDS)) {
        // 执行业务操作
    }
} finally {
    lock.unlock();
}
```

### 6.3 数据校验

在数据访问层进行基础数据校验：

1. **唯一性校验** - 防止重复数据插入
2. **有效性校验** - 检查数据格式和范围
3. **关联性校验** - 检查外键关联关系
4. **业务规则校验** - 检查业务逻辑约束

## 7. 监控和运维

### 7.1 SQL监控

- **慢查询监控** - 监控执行时间超过阈值的SQL
- **执行计划分析** - 定期分析SQL执行计划
- **索引使用率** - 监控索引的使用情况
- **锁等待监控** - 监控数据库锁等待情况

### 7.2 数据质量监控

- **数据一致性检查** - 定期检查数据一致性
- **重复数据检查** - 使用getDupList方法检查重复数据
- **失效数据清理** - 定期清理失效和过期数据
- **数据统计分析** - 监控数据增长趋势和分布

这个数据访问层的详细分析为理解整个系统的数据操作提供了基础，接下来将分析Service层的业务逻辑实现。
